import logging
from fastapi import API<PERSON><PERSON><PERSON>, Body, Query
from tortoise.expressions import Q

from app.controllers.scheduled_task import scheduled_task_controller
from app.schemas.base import Fail, Success, SuccessExtra
from app.schemas.scheduled_task import ScheduledTaskCreate, ScheduledTaskUpdate
from app.core.ctx import CTX_USER_ID
from app.core.dependency import DependAuth
from app.core.scheduler import task_scheduler

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/list", summary="获取定时任务列表")
async def get_scheduled_task_list(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    task_name: str = Query("", description="任务名称"),
    plan_id: int = Query(None, description="测试计划ID"),
    is_active: bool = Query(None, description="是否启用"),
):
    # 构建查询条件
    q = Q()
    if task_name:
        q &= Q(task_name__icontains=task_name)
    if plan_id:
        q &= Q(plan_id=plan_id)
    if is_active is not None:
        q &= Q(is_active=is_active)

    total, data = await scheduled_task_controller.list_with_user_info(
        page=page, page_size=page_size, search=q
    )
    return SuccessExtra(data=data, total=total, page=page, page_size=page_size)


@router.get("/get", summary="获取定时任务详情")
async def get_scheduled_task(
    task_id: int = Query(..., description="任务ID"),
):
    task_obj = await scheduled_task_controller.get(id=task_id)
    task_dict = await task_obj.to_dict()
    return Success(data=task_dict)


@router.get("/by_plan", summary="获取测试计划的定时任务")
async def get_tasks_by_plan(
    plan_id: int = Query(..., description="测试计划ID"),
):
    tasks = await scheduled_task_controller.list_by_plan(plan_id=plan_id)
    return Success(data=tasks)


@router.post("/create", summary="创建定时任务", dependencies=[DependAuth])
async def create_scheduled_task(
    task_in: ScheduledTaskCreate,
):
    try:
        user_id = CTX_USER_ID.get()

        # 检查该测试计划是否已有定时任务
        existing_tasks = await scheduled_task_controller.list_by_plan(plan_id=task_in.plan_id)
        if existing_tasks:
            return Fail(msg="该测试计划已存在定时任务，一个测试计划只能设置一个定时任务")

        # 创建定时任务
        task = await scheduled_task_controller.create_with_user(obj_in=task_in, user_id=user_id)

        # 如果任务启用，添加到调度器
        if task.is_active:
            await task_scheduler.add_job(task.id, task.cron_expression, task.plan_id)

        return Success(msg="定时任务创建成功")
    except ValueError as e:
        return Fail(msg=str(e))
    except Exception as e:
        logger.error(f"创建定时任务失败: {str(e)}")
        return Fail(msg="创建定时任务失败")


@router.post("/update", summary="更新定时任务", dependencies=[DependAuth])
async def update_scheduled_task(
    task_in: ScheduledTaskUpdate = Body(...),
):
    try:
        # 获取更新前的任务信息
        old_task = await scheduled_task_controller.get(id=task_in.id)

        # 更新任务
        updated_task = await scheduled_task_controller.update_task(task_id=task_in.id, obj_in=task_in)

        # 更新调度器中的任务
        if updated_task.is_active:
            await task_scheduler.update_job(updated_task.id, updated_task.cron_expression, updated_task.plan_id)
        else:
            await task_scheduler.remove_job(updated_task.id)

        return Success(msg="定时任务更新成功")
    except ValueError as e:
        return Fail(msg=str(e))
    except Exception as e:
        logger.error(f"更新定时任务失败: {str(e)}")
        return Fail(msg="更新定时任务失败")


@router.post("/toggle", summary="切换任务启用状态", dependencies=[DependAuth])
async def toggle_task_status(
    task_id: int = Body(..., description="任务ID"),
):
    try:
        task = await scheduled_task_controller.toggle_task_status(task_id=task_id)

        # 同步调度器状态
        if task.is_active:
            await task_scheduler.add_job(task.id, task.cron_expression, task.plan_id)
        else:
            await task_scheduler.remove_job(task.id)

        status_text = "启用" if task.is_active else "禁用"
        return Success(msg=f"任务已{status_text}")
    except Exception as e:
        logger.error(f"切换任务状态失败: {str(e)}")
        return Fail(msg="切换任务状态失败")


@router.delete("/delete", summary="删除定时任务", dependencies=[DependAuth])
async def delete_scheduled_task(
    task_id: int = Query(..., description="任务ID"),
):
    try:
        # 先从调度器中删除任务
        await task_scheduler.remove_job(task_id)

        # 再从数据库中删除
        await scheduled_task_controller.remove(id=task_id)

        return Success(msg="定时任务删除成功")
    except Exception as e:
        logger.error(f"删除定时任务失败: {str(e)}")
        return Fail(msg="删除定时任务失败")


@router.post("/validate_cron", summary="验证Cron表达式")
async def validate_cron_expression(
    request: dict = Body(...),
):
    try:
        cron_expression = request.get("cron_expression")
        if not cron_expression:
            return Fail(msg="Cron表达式不能为空")

        is_valid = scheduled_task_controller.validate_cron_expression(cron_expression)
        if is_valid:
            next_run_time = scheduled_task_controller.get_next_run_time(cron_expression)
            return Success(data={
                "valid": True,
                "next_run_time": next_run_time.isoformat() if next_run_time else None
            }, msg="Cron表达式有效")
        else:
            return Fail(msg="无效的Cron表达式")
    except Exception as e:
        logger.error(f"验证Cron表达式失败: {str(e)}")
        return Fail(msg="验证Cron表达式失败")


@router.post("/sync_scheduler", summary="同步定时任务到调度器", dependencies=[DependAuth])
async def sync_tasks_to_scheduler():
    """手动同步数据库中的定时任务到调度器"""
    try:
        # 获取所有启用的定时任务
        active_tasks = await scheduled_task_controller.get_active_tasks()

        # 清空调度器中的所有任务
        jobs = task_scheduler.list_jobs()
        for job in jobs:
            if job['id'].startswith('scheduled_task_'):
                task_id = int(job['id'].replace('scheduled_task_', ''))
                await task_scheduler.remove_job(task_id)

        # 重新添加所有启用的任务
        synced_count = 0
        for task in active_tasks:
            await task_scheduler.add_job(task.id, task.cron_expression, task.plan_id)
            synced_count += 1

        return Success(msg=f"成功同步 {synced_count} 个定时任务到调度器")
    except Exception as e:
        logger.error(f"同步定时任务失败: {str(e)}")
        return Fail(msg="同步定时任务失败")

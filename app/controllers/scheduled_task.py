import logging
from datetime import datetime
from typing import List, Tuple, Optional, Dict, Any
from tortoise.expressions import Q
from croniter import croniter

from app.core.crud import CRUDBase
from app.models.admin import ScheduledTask, ApiTestPlan, User
from app.schemas.scheduled_task import ScheduledTaskCreate, ScheduledTaskUpdate

logger = logging.getLogger(__name__)


class ScheduledTaskController(CRUDBase[ScheduledTask, ScheduledTaskCreate, ScheduledTaskUpdate]):
    def __init__(self):
        super().__init__(model=ScheduledTask)

    async def create_with_user(self, obj_in: ScheduledTaskCreate, user_id: int) -> ScheduledTask:
        """创建定时任务并设置创建人"""
        # 验证cron表达式
        if not self.validate_cron_expression(obj_in.cron_expression):
            raise ValueError("无效的Cron表达式")
        
        # 计算下次执行时间
        next_run_time = self.get_next_run_time(obj_in.cron_expression)
        
        obj_dict = obj_in.model_dump()
        obj_dict['creator_id'] = user_id
        obj_dict['next_run_time'] = next_run_time
        
        task = self.model(**obj_dict)
        await task.save()
        return task

    async def list_with_user_info(self, page: int, page_size: int, search: Q = Q()) -> Tuple[int, List[Dict[str, Any]]]:
        """获取定时任务列表并包含用户信息"""
        query = self.model.filter(search)
        total = await query.count()
        
        tasks = await query.offset((page - 1) * page_size).limit(page_size).order_by('-created_at')
        
        result = []
        for task in tasks:
            task_dict = await task.to_dict()
            
            # 获取创建人信息
            try:
                creator = await User.get(id=task.creator_id)
                task_dict['creator_name'] = creator.username
            except:
                task_dict['creator_name'] = '未知用户'
            
            # 获取测试计划信息
            try:
                plan = await ApiTestPlan.get(id=task.plan_id)
                task_dict['plan_name'] = plan.plan_name
            except:
                task_dict['plan_name'] = '未知计划'
            
            result.append(task_dict)
        
        return total, result

    async def list_by_plan(self, plan_id: int) -> List[Dict[str, Any]]:
        """获取指定测试计划的定时任务"""
        tasks = await self.model.filter(plan_id=plan_id).order_by('-created_at')
        
        result = []
        for task in tasks:
            task_dict = await task.to_dict()
            
            # 获取创建人信息
            try:
                creator = await User.get(id=task.creator_id)
                task_dict['creator_name'] = creator.username
            except:
                task_dict['creator_name'] = '未知用户'
            
            result.append(task_dict)
        
        return result

    async def update_task(self, task_id: int, obj_in: ScheduledTaskUpdate) -> ScheduledTask:
        """更新定时任务"""
        task = await self.get(id=task_id)
        
        update_data = obj_in.model_dump(exclude_unset=True)
        
        # 如果更新了cron表达式，需要重新计算下次执行时间
        if 'cron_expression' in update_data:
            if not self.validate_cron_expression(update_data['cron_expression']):
                raise ValueError("无效的Cron表达式")
            update_data['next_run_time'] = self.get_next_run_time(update_data['cron_expression'])
        
        await self.update(id=task_id, obj_in=update_data)
        return await self.get(id=task_id)

    async def toggle_task_status(self, task_id: int) -> ScheduledTask:
        """切换任务启用状态"""
        task = await self.get(id=task_id)
        new_status = not task.is_active
        
        update_data = {'is_active': new_status}
        if new_status:
            # 如果启用任务，重新计算下次执行时间
            update_data['next_run_time'] = self.get_next_run_time(task.cron_expression)
        else:
            # 如果禁用任务，清空下次执行时间
            update_data['next_run_time'] = None
        
        await self.update(id=task_id, obj_in=update_data)
        return await self.get(id=task_id)

    async def update_run_info(self, task_id: int, success: bool = True):
        """更新任务执行信息"""
        task = await self.get(id=task_id)
        
        update_data = {
            'last_run_time': datetime.now(),
            'run_count': task.run_count + 1
        }
        
        # 计算下次执行时间
        if task.is_active:
            update_data['next_run_time'] = self.get_next_run_time(task.cron_expression)
        
        await self.update(id=task_id, obj_in=update_data)

    @staticmethod
    def validate_cron_expression(cron_expr: str) -> bool:
        """验证cron表达式是否有效"""
        try:
            croniter(cron_expr)
            return True
        except Exception:
            return False

    @staticmethod
    def get_next_run_time(cron_expr: str) -> Optional[datetime]:
        """根据cron表达式计算下次执行时间"""
        try:
            cron = croniter(cron_expr, datetime.now())
            return cron.get_next(datetime)
        except Exception:
            return None

    async def get_active_tasks(self) -> List[ScheduledTask]:
        """获取所有启用的定时任务"""
        return await self.model.filter(is_active=True)


# 创建控制器实例
scheduled_task_controller = ScheduledTaskController()

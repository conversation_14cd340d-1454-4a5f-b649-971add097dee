import { defineStore } from 'pinia'
import { basicRoutes, vueModules } from '@/router/routes'
import Layout from '@/layout/index.vue'
import api from '@/api'

// * 后端路由相关函数
// 根据后端传来数据构建出前端路由

function buildRoutes(routes = []) {
  // 递归处理子菜单的函数
  function buildChildRoutes(children) {
    return children.map((child) => {
      // 尝试多种可能的组件路径
      let component = null
      const possiblePaths = [
        `/src/views${child.component}/index.vue`,
        `/src/views${child.component}.vue`
      ]

      for (const path of possiblePaths) {
        if (vueModules[path]) {
          component = vueModules[path]
          break
        }
      }

      // 如果找不到组件，输出警告
      if (!component) {
        console.warn(`Component not found for path: ${child.component}`, possiblePaths)
      }

      const childRoute = {
        name: child.name,
        path: child.path,
        component: component,
        isHidden: child.is_hidden,
        meta: {
          title: child.name,
          icon: child.icon,
          order: child.order,
          keepAlive: child.keepalive,
        },
      }

      // 如果子菜单还有子菜单，递归处理
      if (child.children && child.children.length > 0) {
        childRoute.children = buildChildRoutes(child.children)
      }

      return childRoute
    })
  }

  return routes.map((e) => {
    const route = {
      name: e.name,
      path: e.path,
      component: shallowRef(Layout),
      isHidden: e.is_hidden,
      redirect: e.redirect,
      meta: {
        title: e.name,
        icon: e.icon,
        order: e.order,
        keepAlive: e.keepalive,
      },
      children: [],
    }

    if (e.children && e.children.length > 0) {
      // 有子菜单，递归处理
      route.children = buildChildRoutes(e.children)
    } else {
      // 没有子菜单，创建一个默认的子路由
      // 尝试多种可能的组件路径
      let component = null
      const possiblePaths = [
        `/src/views${e.component}/index.vue`,
        `/src/views${e.component}.vue`
      ]

      for (const path of possiblePaths) {
        if (vueModules[path]) {
          component = vueModules[path]
          break
        }
      }

      // 如果找不到组件，输出警告
      if (!component) {
        console.warn(`Component not found for path: ${e.component}`, possiblePaths)
      }

      route.children.push({
        name: `${e.name}Default`,
        path: '',
        component: component,
        isHidden: true,
        meta: {
          title: e.name,
          icon: e.icon,
          order: e.order,
          keepAlive: e.keepalive,
        },
      })
    }

    return route
  })
}

export const usePermissionStore = defineStore('permission', {
  state() {
    return {
      accessRoutes: [],
      accessApis: [],
    }
  },
  getters: {
    routes() {
      return basicRoutes.concat(this.accessRoutes)
    },
    menus() {
      return this.routes.filter((route) => route.name && !route.isHidden)
    },
    apis() {
      return this.accessApis
    },
  },
  actions: {
    async generateRoutes() {
      console.log('🔄 开始生成动态路由...')
      const res = await api.getUserMenu() // 调用接口获取后端传来的菜单路由
      console.log('📡 后端菜单数据:', res.data)

      // 查找功能测试计划相关菜单
      const findFunctionalTestPlan = (menus) => {
        for (const menu of menus) {
          if (menu.name === '功能测试计划' && menu.children) {
            for (const child of menu.children) {
              if (child.name === '功能测试计划详情') {
                console.log('🎯 找到功能测试计划详情菜单:', child)
                return child
              }
            }
          }
          if (menu.children) {
            const found = findFunctionalTestPlan(menu.children)
            if (found) return found
          }
        }
        return null
      }

      const functionalDetail = findFunctionalTestPlan(res.data)
      if (functionalDetail) {
        console.log('✅ 功能测试计划详情菜单存在于后端数据中')
      } else {
        console.log('❌ 功能测试计划详情菜单不存在于后端数据中')
      }

      this.accessRoutes = buildRoutes(res.data) // 处理成前端路由格式
      console.log('🛠️ 生成的路由:', this.accessRoutes)

      // 查找生成的路由中是否包含功能测试计划详情
      const findRouteInGenerated = (routes) => {
        for (const route of routes) {
          if (route.name === '功能测试计划' && route.children) {
            for (const child of route.children) {
              if (child.name === '功能测试计划详情') {
                console.log('🎯 在生成的路由中找到功能测试计划详情:', child)
                return child
              }
            }
          }
          if (route.children) {
            const found = findRouteInGenerated(route.children)
            if (found) return found
          }
        }
        return null
      }

      const generatedDetail = findRouteInGenerated(this.accessRoutes)
      if (generatedDetail) {
        console.log('✅ 功能测试计划详情路由已生成')
      } else {
        console.log('❌ 功能测试计划详情路由未生成')
      }

      return this.accessRoutes
    },
    async getAccessApis() {
      const res = await api.getUserApi()
      this.accessApis = res.data
      return this.accessApis
    },
    resetPermission() {
      this.$reset()
    },
  },
})

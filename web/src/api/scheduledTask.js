import { request } from '@/utils'

export default {
  // 获取定时任务列表
  getScheduledTaskList: (params) => request.get('/scheduled_task/list', { params }),
  
  // 获取定时任务详情
  getScheduledTask: (params) => request.get('/scheduled_task/get', { params }),
  
  // 获取测试计划的定时任务
  getTasksByPlan: (params) => request.get('/scheduled_task/by_plan', { params }),
  
  // 创建定时任务
  createScheduledTask: (data) => request.post('/scheduled_task/create', data),
  
  // 更新定时任务
  updateScheduledTask: (data) => request.post('/scheduled_task/update', data),
  
  // 切换任务启用状态
  toggleTaskStatus: (data) => request.post('/scheduled_task/toggle', data),
  
  // 删除定时任务
  deleteScheduledTask: (params) => request.delete('/scheduled_task/delete', { params }),
  
  // 验证Cron表达式
  validateCronExpression: (data) => request.post('/scheduled_task/validate_cron', data)
}

<script setup>
import { h, onMounted, ref, resolveDirective, withDirectives } from 'vue'
import { NButton, NForm, NFormItem, NInput, NPopconfirm, NSelect, NInputNumber, NSwitch, NTag, NDivider, NSpace, NGrid, NGridItem } from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import { NLayout, NLayoutSider, NLayoutContent, NTree } from 'naive-ui'

import { renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import environmentApi from '@/api/environment'
import projectApi from '@/api/project'

defineOptions({ name: '环境配置' })

const $table = ref(null)
const queryItems = ref({
  project_id: null,
  name: '',
  env_type: null
})
const vPermission = resolveDirective('permission')

// 项目相关
const projectOption = ref([])
const selectedKeys = ref([])
const selectedProjectId = ref(null)
const selectedProjectName = ref('')

// 环境类型选项
const envTypeOptions = [
  { label: '开发环境', value: 'development' },
  { label: '测试环境', value: 'testing' },
  { label: '预发布环境', value: 'staging' },
  { label: '生产环境', value: 'production' }
]

// 复制环境相关
const copyModalVisible = ref(false)
const copyForm = ref({
  id: null,
  name: '',
  project_id: null
})

// Token操作相关
const testTokenLoading = ref(false)
const refreshTokenLoading = ref(false)

// 验证码操作相关
const testCaptchaLoading = ref(false)
const captchaData = ref({
  imageBase64: '',
  codeKey: '',
  recognizedCode: '',
  recognitionMessage: ''
})

const {
  modalVisible,
  modalTitle,
  modalLoading,
  modalAction,
  handleSave,
  modalForm,
  modalFormRef,
  handleEdit,
  handleDelete,
  handleAdd,
} = useCRUD({
  name: '环境配置',
  initForm: {
    name: '',
    env_type: 'development',
    host: '',
    port: 80,
    token: '',
    description: '',
    project_id: null,
    is_active: true,
    // Token自动获取配置
    token_url: '',
    token_method: 'POST',
    token_headers: {},
    token_body: {},
    token_field_name: 'token',
    token_field_path: '',
    auto_refresh_token: false,
    token_refresh_interval: 3600,
    // 验证码配置
    enable_captcha: false,
    captcha_url: '',
    captcha_method: 'GET',
    captcha_headers: {},
    captcha_body: {},
    captcha_image_path: 'content.imageBase64',
    captcha_key_path: 'content.codeKey'
  },
  doCreate: environmentApi.createEnvironment,
  doUpdate: environmentApi.updateEnvironment,
  doDelete: environmentApi.deleteEnvironment,
  refresh: () => $table.value?.handleSearch(),
})

// 项目树节点属性
const nodeProps = ({ option }) => {
  return {
    onClick() {
      handleProjectSelect(option.id, option.name)
    },
  }
}

// 处理项目选择
const handleProjectSelect = async (projectId, projectName) => {
  selectedKeys.value = [projectId]
  selectedProjectId.value = projectId
  selectedProjectName.value = projectName
  queryItems.value.project_id = projectId
  $table.value?.handleSearch()
}

// 加载项目列表
const loadProjectList = async () => {
  try {
    const response = await projectApi.getProjectList()
    projectOption.value = response.data || []

    // 默认选中第一个项目
    if (projectOption.value.length > 0) {
      const firstProject = projectOption.value[0]
      selectedProjectId.value = firstProject.id
      selectedKeys.value = [firstProject.id]
      selectedProjectName.value = firstProject.name
      queryItems.value.project_id = firstProject.id

      // 刷新表格数据
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

// 自定义编辑处理
const customHandleEdit = (row) => {
  modalAction.value = 'edit'
  modalForm.value = { ...row }
  modalForm.value.project_id = selectedProjectId.value

  // 处理JSON字段的显示
  if (modalForm.value.token_headers && typeof modalForm.value.token_headers === 'object') {
    modalForm.value.token_headers = JSON.stringify(modalForm.value.token_headers, null, 2)
  }
  if (modalForm.value.token_body && typeof modalForm.value.token_body === 'object') {
    modalForm.value.token_body = JSON.stringify(modalForm.value.token_body, null, 2)
  }
  // 处理验证码相关JSON字段
  if (modalForm.value.captcha_headers && typeof modalForm.value.captcha_headers === 'object') {
    modalForm.value.captcha_headers = JSON.stringify(modalForm.value.captcha_headers, null, 2)
  }
  if (modalForm.value.captcha_body && typeof modalForm.value.captcha_body === 'object') {
    modalForm.value.captcha_body = JSON.stringify(modalForm.value.captcha_body, null, 2)
  }

  modalVisible.value = true
}

// 自定义新增处理
const customHandleAdd = () => {
  if (!selectedProjectId.value) {
    window.$message?.error('请先选择项目')
    return
  }
  modalAction.value = 'add'
  modalForm.value = {
    name: '',
    env_type: 'development',
    host: '',
    port: 80,
    token: '',
    description: '',
    project_id: selectedProjectId.value,
    is_active: true,
    // Token自动获取配置
    token_url: '',
    token_method: 'POST',
    token_headers: '',
    token_body: '',
    token_field_name: 'token',
    token_field_path: '',
    auto_refresh_token: false,
    token_refresh_interval: 3600,
    // 验证码配置
    enable_captcha: false,
    captcha_url: '',
    captcha_method: 'GET',
    captcha_headers: '',
    captcha_body: '',
    captcha_image_path: 'content.imageBase64',
    captcha_key_path: 'content.codeKey'
  }
  modalVisible.value = true
  console.log('新增环境配置，modalAction:', modalAction.value, 'modalForm:', modalForm.value)
}

// 复制环境
const handleCopy = (row) => {
  copyForm.value = {
    id: row.id,
    name: `${row.name}_copy`,
    project_id: selectedProjectId.value
  }
  copyModalVisible.value = true
}

// 自定义保存处理
const customHandleSave = async () => {
  console.log('开始保存，modalAction:', modalAction.value, 'modalForm:', modalForm.value)

  if (!modalFormRef.value) {
    console.error('modalFormRef 未找到')
    return
  }

  modalFormRef.value.validate(async (errors) => {
    if (errors) {
      console.log('表单验证失败:', errors)
      return
    }

    try {
      modalLoading.value = true

      // 处理JSON字段
      const formData = { ...modalForm.value }

      // 处理端口号字段 - 将空字符串转换为null
      if (formData.port === '' || formData.port === undefined) {
        formData.port = null
      } else if (typeof formData.port === 'string') {
        // 尝试转换为数字
        const portNum = parseInt(formData.port, 10)
        if (isNaN(portNum)) {
          formData.port = null
        } else {
          formData.port = portNum
        }
      }

      // 解析JSON字符串
      // 处理token_headers
      if (formData.token_headers) {
        if (typeof formData.token_headers === 'string') {
          if (formData.token_headers.trim() === '') {
            formData.token_headers = {}
          } else {
            try {
              formData.token_headers = JSON.parse(formData.token_headers)
            } catch (e) {
              console.warn('token_headers JSON解析失败，使用空对象:', e)
              formData.token_headers = {}
            }
          }
        }
      } else {
        formData.token_headers = {}
      }

      // 处理token_body
      if (formData.token_body) {
        if (typeof formData.token_body === 'string') {
          if (formData.token_body.trim() === '') {
            formData.token_body = {}
          } else {
            try {
              formData.token_body = JSON.parse(formData.token_body)
            } catch (e) {
              console.warn('token_body JSON解析失败，使用空对象:', e)
              formData.token_body = {}
            }
          }
        }
      } else {
        formData.token_body = {}
      }

      // 处理验证码相关JSON字段
      // 处理captcha_headers
      if (formData.captcha_headers) {
        if (typeof formData.captcha_headers === 'string') {
          if (formData.captcha_headers.trim() === '') {
            formData.captcha_headers = {}
          } else {
            try {
              formData.captcha_headers = JSON.parse(formData.captcha_headers)
            } catch (e) {
              console.warn('captcha_headers JSON解析失败，使用空对象:', e)
              formData.captcha_headers = {}
            }
          }
        }
      } else {
        formData.captcha_headers = {}
      }

      // 处理captcha_body
      if (formData.captcha_body) {
        if (typeof formData.captcha_body === 'string') {
          if (formData.captcha_body.trim() === '') {
            formData.captcha_body = {}
          } else {
            try {
              formData.captcha_body = JSON.parse(formData.captcha_body)
            } catch (e) {
              console.warn('captcha_body JSON解析失败，使用空对象:', e)
              formData.captcha_body = {}
            }
          }
        }
      } else {
        formData.captcha_body = {}
      }

      // 如果未启用自动刷新，清空相关字段
      if (!formData.auto_refresh_token) {
        formData.token_url = ''
        formData.token_method = 'POST'
        formData.token_headers = {}
        formData.token_body = {}
        formData.token_field_name = 'token'
        formData.token_field_path = ''
        formData.token_refresh_interval = 3600
      }

      console.log('准备调用API，数据:', formData)

      if (modalAction.value === 'add') {
        await environmentApi.createEnvironment(formData)
        window.$message?.success('环境配置创建成功')
      } else if (modalAction.value === 'edit') {
        await environmentApi.updateEnvironment(formData)
        window.$message?.success('环境配置更新成功')
      }

      modalVisible.value = false
      $table.value?.handleSearch()
    } catch (error) {
      console.error('保存失败:', error)
      window.$message?.error('保存失败: ' + (error.message || '未知错误'))
    } finally {
      modalLoading.value = false
    }
  })
}

// 执行复制
const handleCopySubmit = async () => {
  try {
    await environmentApi.copyEnvironment(copyForm.value)
    window.$message?.success('环境配置复制成功')
    copyModalVisible.value = false
    $table.value?.handleSearch()
  } catch (error) {
    console.error('复制失败:', error)
    window.$message?.error('复制失败')
  }
}

// 测试Token配置
const handleTestToken = async () => {
  if (!modalForm.value.id) {
    window.$message?.error('请先保存环境配置')
    return
  }

  try {
    testTokenLoading.value = true
    const response = await environmentApi.testTokenConfig({ environment_id: modalForm.value.id })

    if (response.code === 200) {
      window.$message?.success(`Token配置测试成功: ${response.data.token}`)
    } else {
      window.$message?.error(response.msg || 'Token配置测试失败')
    }
  } catch (error) {
    console.error('测试Token配置失败:', error)
    window.$message?.error('测试Token配置失败')
  } finally {
    testTokenLoading.value = false
  }
}

// 刷新Token
const handleRefreshToken = async () => {
  if (!modalForm.value.id) {
    window.$message?.error('请先保存环境配置')
    return
  }

  try {
    refreshTokenLoading.value = true
    const response = await environmentApi.refreshToken({ environment_id: modalForm.value.id })

    if (response.code === 200) {
      window.$message?.success('Token刷新成功')

      // 重新获取环境配置数据来更新表单
      const envResponse = await environmentApi.getEnvironment({ environment_id: modalForm.value.id })
      if (envResponse.code === 200) {
        const envData = envResponse.data

        // 更新表单中的token值
        modalForm.value.token = envData.token

        // 处理JSON字段的显示
        if (envData.token_headers && typeof envData.token_headers === 'object') {
          modalForm.value.token_headers = JSON.stringify(envData.token_headers, null, 2)
        }
        if (envData.token_body && typeof envData.token_body === 'object') {
          modalForm.value.token_body = JSON.stringify(envData.token_body, null, 2)
        }

        console.log('Token刷新后更新的表单数据:', modalForm.value)
      }
    } else {
      window.$message?.error(response.msg || 'Token刷新失败')
    }
  } catch (error) {
    console.error('刷新Token失败:', error)
    window.$message?.error('刷新Token失败')
  } finally {
    refreshTokenLoading.value = false
  }
}

// 测试验证码地址
const handleTestCaptcha = async () => {
  if (!modalForm.value.captcha_url) {
    window.$message?.error('请先输入验证码地址')
    return
  }

  try {
    testCaptchaLoading.value = true

    // 准备请求参数
    let captcha_headers = '{}'
    let captcha_body = '{}'

    // 处理请求头
    if (modalForm.value.captcha_headers) {
      if (typeof modalForm.value.captcha_headers === 'string') {
        captcha_headers = modalForm.value.captcha_headers
      } else {
        captcha_headers = JSON.stringify(modalForm.value.captcha_headers)
      }
    }

    // 处理请求体
    if (modalForm.value.captcha_body) {
      if (typeof modalForm.value.captcha_body === 'string') {
        captcha_body = modalForm.value.captcha_body
      } else {
        captcha_body = JSON.stringify(modalForm.value.captcha_body)
      }
    }

    const response = await environmentApi.testCaptchaUrl({
      captcha_url: modalForm.value.captcha_url,
      captcha_method: modalForm.value.captcha_method || 'GET',
      captcha_headers: captcha_headers,
      captcha_body: captcha_body,
      captcha_image_path: modalForm.value.captcha_image_path || 'content.imageBase64',
      captcha_key_path: modalForm.value.captcha_key_path || 'content.codeKey'
    })

    if (response.code === 200) {
      captchaData.value = {
        imageBase64: response.data.imageBase64,
        codeKey: response.data.codeKey,
        recognizedCode: response.data.recognizedCode,
        recognitionMessage: response.data.recognitionMessage
      }

      // 根据识别结果显示不同的消息
      if (response.data.recognizedCode) {
        window.$message?.success(`验证码地址测试成功，识别结果: ${response.data.recognizedCode}`)
      } else {
        window.$message?.warning('验证码地址测试成功，但识别失败')
      }
    } else {
      window.$message?.error(response.msg || '验证码地址测试失败')
    }
  } catch (error) {
    console.error('测试验证码地址失败:', error)
    window.$message?.error('测试验证码地址失败')
  } finally {
    testCaptchaLoading.value = false
  }
}

// 保存基本信息
const handleSaveBasicInfo = async () => {
  await customHandleSave()
}

// 保存Token配置
const handleSaveTokenConfig = async () => {
  await customHandleSave()
  window.$message?.success('Token配置保存成功')
}

// 保存验证码配置
const handleSaveCaptchaConfig = async () => {
  await customHandleSave()
  window.$message?.success('验证码配置保存成功')
}

// 表格列定义
const columns = [
  {
    title: '环境名称',
    key: 'name',
    width: 150,
    align: 'center',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '环境类型',
    key: 'env_type',
    width: 120,
    align: 'center',
    render(row) {
      const typeMap = {
        development: { label: '开发', color: 'info' },
        testing: { label: '测试', color: 'warning' },
        staging: { label: '预发布', color: 'default' },
        production: { label: '生产', color: 'error' }
      }
      const type = typeMap[row.env_type] || { label: row.env_type, color: 'default' }
      return h(NTag, { type: type.color, size: 'small' }, { default: () => type.label })
    }
  },
  {
    title: '主机地址',
    key: 'host',
    width: 200,
    align: 'center',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '端口',
    key: 'port',
    width: 80,
    align: 'center'
  },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    align: 'center',
    render(row) {
      return h(NTag, { 
        type: row.is_active ? 'success' : 'default',
        size: 'small'
      }, { 
        default: () => row.is_active ? '启用' : '禁用' 
      })
    }
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    align: 'center',
    ellipsis: {
      tooltip: true
    },
    render(row) {
      return row.description || '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    align: 'center',
    fixed: 'right',
    render(row) {
      return [
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'info',
              style: 'margin-right: 8px;',
              onClick: () => handleCopy(row),
            },
            {
              default: () => '复制',
            }
          ),
          [[vPermission, 'post/api/v1/environment/copy']]
        ),
        withDirectives(
          h(
            NButton,
            {
              size: 'small',
              type: 'primary',
              style: 'margin-right: 8px;',
              onClick: () => customHandleEdit(row),
            },
            {
              default: () => '编辑',
            }
          ),
          [[vPermission, 'post/api/v1/environment/update']]
        ),
        withDirectives(
          h(
            NPopconfirm,
            {
              onPositiveClick: () => handleDelete({ id: row.id }),
            },
            {
              default: () => '确定删除吗？',
              trigger: () =>
                h(
                  NButton,
                  {
                    size: 'small',
                    type: 'error',
                  },
                  { default: () => '删除' }
                ),
            }
          ),
          [[vPermission, 'delete/api/v1/environment/delete']]
        ),
      ]
    },
  },
]

// 表单验证规则
const environmentRules = {
  name: [
    { required: true, message: '请输入环境名称', trigger: 'blur' },
    { min: 1, max: 100, message: '环境名称长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  env_type: [
    { required: true, message: '请选择环境类型', trigger: 'change' }
  ],
  host: [
    { required: true, message: '请输入主机地址', trigger: 'blur' },
    { min: 1, max: 255, message: '主机地址长度在 1 到 255 个字符', trigger: 'blur' }
  ],
  port: []
}

onMounted(() => {
  loadProjectList()
})
</script>

<template>
  <NLayout has-sider wh-full>
    <NLayoutSider
      bordered
      content-style="padding: 24px;"
      :collapsed-width="0"
      :width="240"
      show-trigger="arrow-circle"
    >
      <h1>项目列表</h1>
      <br />
      <NTree
        block-line
        :data="projectOption"
        key-field="id"
        label-field="name"
        default-expand-all
        :node-props="nodeProps"
        :selected-keys="selectedKeys"
      >
      </NTree>
    </NLayoutSider>
    <NLayoutContent>
      <CommonPage show-footer :title="selectedProjectName ? `${selectedProjectName} - 环境配置` : '环境配置'">
        <template #action>
          <NButton
            v-if="selectedProjectId"
            v-permission="'post/api/v1/environment/create'"
            type="primary"
            @click="customHandleAdd"
          >
            <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建环境配置
          </NButton>
        </template>
        <!-- 表格 -->
        <CrudTable
          ref="$table"
          v-model:query-items="queryItems"
          :columns="columns"
          :get-data="environmentApi.getEnvironments"
        >
          <template #queryBar>
            <QueryBarItem label="环境名称" :label-width="70">
              <NInput
                v-model:value="queryItems.name"
                clearable
                type="text"
                placeholder="请输入环境名称"
                @keypress.enter="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="环境类型" :label-width="70">
              <NSelect
                v-model:value="queryItems.env_type"
                clearable
                :options="envTypeOptions"
                placeholder="请选择环境类型"
                style="min-width: 150px;"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
          </template>
        </CrudTable>
      </CommonPage>

      <!-- 新增/编辑 弹窗 -->
      <CrudModal
        v-model:visible="modalVisible"
        :title="modalTitle"
        :loading="modalLoading"
        @save="customHandleSave"
        width="900px"
        style="max-height: 90vh; overflow-y: auto;"
      >
        <NTabs type="line" animated>
          <!-- 基本信息 Tab -->
          <NTabPane name="basic" tab="基本信息">
            <NForm
              ref="modalFormRef"
              label-placement="left"
              label-align="left"
              :label-width="80"
              :model="modalForm"
              :rules="environmentRules"
            >
              <NGrid :cols="2" :x-gap="16">
                <NGridItem>
                  <NFormItem label="环境名称" path="name">
                    <NInput v-model:value="modalForm.name" clearable placeholder="请输入环境名称" />
                  </NFormItem>
                </NGridItem>
                <NGridItem>
                  <NFormItem label="环境类型" path="env_type">
                    <NSelect
                      v-model:value="modalForm.env_type"
                      :options="envTypeOptions"
                      placeholder="请选择环境类型"
                    />
                  </NFormItem>
                </NGridItem>
              </NGrid>

              <NGrid :cols="2" :x-gap="16">
                <NGridItem>
                  <NFormItem label="主机地址" path="host">
                    <NInput v-model:value="modalForm.host" clearable placeholder="请输入主机地址" />
                  </NFormItem>
                </NGridItem>
                <NGridItem>
                  <NFormItem label="端口号" path="port">
                    <NInput
                      v-model:value="modalForm.port"
                      clearable
                      placeholder="请输入端口号"
                    />
                  </NFormItem>
                </NGridItem>
              </NGrid>
              
              <NFormItem label="访问令牌" path="token">
                <NInput
                  v-model:value="modalForm.prefix"
                  type="textarea"
                  :rows="3"
                  placeholder="令牌前缀"
                  style="width: 120px; margin-right: 12px;"
                />
                <NInput
                  v-model:value="modalForm.token"
                  type="textarea"
                  :rows="3"
                  clearable
                  placeholder="请输入访问令牌"
                />
            </NFormItem>

              <NFormItem label="环境描述" path="description">
                <NInput v-model:value="modalForm.description" type="textarea" :rows="3" clearable placeholder="请输入环境描述" />
              </NFormItem>

              <NGrid :cols="2" :x-gap="16">
                <NGridItem>
                  <NFormItem label="是否启用" path="is_active">
                    <NSpace align="center">
                      <NSwitch v-model:value="modalForm.is_active" />
                      <span style="font-size: 12px; color: #666;">启用后环境可正常使用</span>
                    </NSpace>
                  </NFormItem>
                </NGridItem>
                <NGridItem>
                  <NFormItem label="自动刷新" path="auto_refresh_token">
                    <NSpace align="center">
                      <NSwitch v-model:value="modalForm.auto_refresh_token" />
                      <span style="font-size: 12px; color: #666;">
                        {{ modalForm.auto_refresh_token ? '已启用自动刷新' : '手动管理Token' }}
                      </span>
                    </NSpace>
                  </NFormItem>
                </NGridItem>
              </NGrid>
            </NForm>

            <!-- 基本信息保存按钮 -->
            <!-- <div style="text-align: right; margin-top: 16px;">
              <NButton type="primary" @click="handleSaveBasicInfo" :loading="modalLoading">
                保存基本信息
              </NButton>
            </div> -->
          </NTabPane>

          <!-- Token自动获取配置 Tab -->
          <NTabPane name="token" tab="Token自动获取配置" :disabled="!modalForm.auto_refresh_token">
            <template v-if="!modalForm.auto_refresh_token">
              <NEmpty description="请先在基本信息中启用自动刷新Token功能">
                <template #extra>
                  <NButton size="small" @click="modalForm.auto_refresh_token = true">
                    启用自动刷新Token
                  </NButton>
                </template>
              </NEmpty>
            </template>

            <template v-else>
              <NForm
                ref="modalFormRef"
                label-placement="left"
                label-align="left"
                :label-width="100"
                :model="modalForm"
              >
                <NGrid :cols="2" :x-gap="16">

                  <NGridItem>
                    <NFormItem label="请求方式" path="token_method">
                      <NSelect
                        v-model:value="modalForm.token_method"
                        :options="[
                          { label: 'GET', value: 'GET' },
                          { label: 'POST', value: 'POST' },
                          { label: 'PUT', value: 'PUT' }
                        ]"
                        placeholder="请选择请求方式"
                      />
                    </NFormItem>
                  </NGridItem>
                  <NGridItem>
                    <NFormItem label="请求链接" path="token_url">
                      <NInput v-model:value="modalForm.token_url" clearable placeholder="支持占位符: ${code}, ${codekey}" />
<!--                      <div style="font-size: 12px; color: #666; margin-top: 4px;">-->
<!--                        💡 支持验证码占位符：${code}, ${codekey} 等-->
<!--                      </div>-->
                    </NFormItem>
                  </NGridItem>
                </NGrid>

                <NGrid :cols="3" :x-gap="16">
                  <NGridItem>
                    <NFormItem label="Token字段名" path="token_field_name">
                      <NInput
                        v-model:value="modalForm.token_field_name"
                        clearable
                        placeholder="如: token"
                      />
                    </NFormItem>
                  </NGridItem>
                  <NGridItem>
                    <NFormItem label="刷新间隔(秒)" path="token_refresh_interval">
                      <NInputNumber
                        v-model:value="modalForm.token_refresh_interval"
                        :min="60"
                        :max="86400"
                        placeholder="60"
                        style="width: 100%;"
                      />
                    </NFormItem>
                  </NGridItem>
                  <NGridItem v-if="modalAction === 'edit' && modalForm.id">
                    <NFormItem label="">
                      <NSpace>
                        <NButton
                          type="info"
                          size="small"
                          @click="handleTestToken"
                          :loading="testTokenLoading"
                        >
                          测试配置
                        </NButton>
                        <NButton
                          type="primary"
                          size="small"
                          @click="handleRefreshToken"
                          :loading="refreshTokenLoading"
                        >
                          立即刷新
                        </NButton>
                      </NSpace>
                    </NFormItem>
                  </NGridItem>
                </NGrid>

                <NFormItem label="Token路径" path="token_field_path">
                  <NInput
                    v-model:value="modalForm.token_field_path"
                    clearable
                    placeholder="JSONPath表达式，如: $.data.token (可选，留空则使用字段名直接提取)"
                  />
                </NFormItem>

                <NGrid :cols="2" :x-gap="16">
                  <NGridItem>
                    <NFormItem label="请求头" path="token_headers">
                      <NInput
                        v-model:value="modalForm.token_headers"
                        type="textarea"
                        :rows="4"
                        clearable
                        placeholder='JSON格式，例如: {"Content-Type": "application/json"}'
                      />
                    </NFormItem>
                  </NGridItem>
                  <NGridItem>
                    <NFormItem label="请求体" path="token_body">
                      <NInput
                        v-model:value="modalForm.token_body"
                        type="textarea"
                        :rows="4"
                        clearable
                        placeholder='支持占位符: {"code": "${code}", "codeKey": "${codekey}", "username": "admin", "password": "123456"}'
                      />
                    </NFormItem>
                  </NGridItem>
                </NGrid>
              </NForm>

              <!-- Token配置保存按钮 -->
              <!-- <div style="text-align: right; margin-top: 16px;">
                <NButton type="primary" @click="handleSaveTokenConfig" :loading="modalLoading">
                  保存Token配置
                </NButton>
              </div> -->
            </template>
          </NTabPane>

          <!-- 验证码配置 Tab -->
          <NTabPane name="captcha" tab="验证码配置" :disabled="!modalForm.auto_refresh_token">
            <template v-if="!modalForm.auto_refresh_token">
              <NEmpty description="请先在基本信息中启用自动刷新Token功能">
                <template #extra>
                  <NButton size="small" @click="modalForm.auto_refresh_token = true">
                    启用自动刷新Token
                  </NButton>
                </template>
              </NEmpty>
            </template>

            <template v-else>
              <NForm
                ref="modalFormRef"
                label-placement="left"
                label-align="left"
                :label-width="100"
                :model="modalForm"
              >
                <!-- 验证码开关 -->
                <NFormItem label="启用验证码" path="enable_captcha">
                  <NSpace align="center">
                    <NSwitch v-model:value="modalForm.enable_captcha" />
                    <span style="font-size: 12px; color: #666;">
                      {{ modalForm.enable_captcha ? '已启用验证码自动识别' : '不使用验证码' }}
                    </span>
                  </NSpace>
                </NFormItem>

                <template v-if="modalForm.enable_captcha">
                  <!-- 基本配置 -->
                  <NGrid :cols="2" :x-gap="16">
                    <NGridItem>
                      <NFormItem label="验证码地址" path="captcha_url">
                        <NInput v-model:value="modalForm.captcha_url" clearable placeholder="请输入验证码获取URL" />
                      </NFormItem>
                    </NGridItem>
                    <NGridItem>
                      <NFormItem label="请求方式" path="captcha_method">
                        <NSelect
                          v-model:value="modalForm.captcha_method"
                          :options="[
                            { label: 'GET', value: 'GET' },
                            { label: 'POST', value: 'POST' },
                            { label: 'PUT', value: 'PUT' },
                            { label: 'DELETE', value: 'DELETE' }
                          ]"
                          placeholder="请选择请求方式"
                        />
                      </NFormItem>
                    </NGridItem>
                  </NGrid>

                  <!-- 响应数据解析 -->
                  <NGrid :cols="2" :x-gap="16">
                    <NGridItem>
                      <NFormItem label="图片字段路径" path="captcha_image_path">
                        <NInput
                          v-model:value="modalForm.captcha_image_path"
                          clearable
                          placeholder="如: content.imageBase64 或 data.image"
                        />
                      </NFormItem>
                    </NGridItem>
                    <NGridItem>
                      <NFormItem label="Key字段路径" path="captcha_key_path">
                        <NInput
                          v-model:value="modalForm.captcha_key_path"
                          clearable
                          placeholder="如: content.codeKey 或 data.key"
                        />
                      </NFormItem>
                    </NGridItem>
                  </NGrid>

                  <!-- 高级配置（仅在POST/PUT时显示） -->
                  <template v-if="modalForm.captcha_method === 'POST' || modalForm.captcha_method === 'PUT'">
                    <NDivider title-placement="left" style="margin: 16px 0 8px 0;">
                      <span style="font-size: 12px; color: #666;">高级请求配置</span>
                    </NDivider>

                    <NGrid :cols="2" :x-gap="16">
                      <NGridItem>
                        <NFormItem label="请求头" path="captcha_headers">
                          <NInput
                            v-model:value="modalForm.captcha_headers"
                            type="textarea"
                            :rows="3"
                            clearable
                            placeholder='JSON格式，例如: {"Content-Type": "application/json", "Authorization": "Bearer token"}'
                          />
                        </NFormItem>
                      </NGridItem>
                      <NGridItem>
                        <NFormItem label="请求体" path="captcha_body">
                          <NInput
                            v-model:value="modalForm.captcha_body"
                            type="textarea"
                            :rows="3"
                            clearable
                            placeholder='JSON格式，例如: {"type": "captcha", "refresh": true}'
                          />
                        </NFormItem>
                      </NGridItem>
                    </NGrid>
                  </template>

                  <!-- 配置说明 -->
                  <NAlert type="info" style="margin: 16px 0;">
                    <template #header>
                      <span style="font-size: 14px;">配置说明</span>
                    </template>
                    <div style="font-size: 12px; line-height: 1.6;">
                      <strong>JSON路径示例：</strong><br/>
                      • 直接字段：<code>imageBase64</code>, <code>codeKey</code><br/>
                      • 嵌套字段：<code>content.imageBase64</code>, <code>data.captcha.image</code><br/>
                      • 数组元素：<code>data[0].image</code>, <code>result[*].key</code><br/><br/>
                      <strong>占位符支持：</strong><br/>
                      系统会自动识别验证码并替换Token请求体中的 <code>${code}</code> 和 <code>${codekey}</code> 占位符
                    </div>
                  </NAlert>

                  <!-- 验证码测试和预览 -->
                  <NFormItem v-if="modalAction === 'edit' && modalForm.id" label="验证码测试">
                    <NSpace vertical>
                      <NSpace align="center">
                        <NButton
                          type="primary"
                          @click="handleTestCaptcha"
                          :loading="testCaptchaLoading"
                          :disabled="!modalForm.captcha_url"
                        >
                          <TheIcon icon="material-symbols:security" :size="16" class="mr-1" />
                          测试验证码地址
                        </NButton>

                        <span v-if="captchaData.codeKey" style="color: #18a058; font-size: 12px;">
                          ✓ 验证码获取成功
                        </span>
                      </NSpace>

                      <!-- 验证码预览 -->
                      <div v-if="captchaData.imageBase64" style="padding: 12px; background-color: #fafafa; border-radius: 6px; border: 1px solid #e0e0e0;">
                        <div style="font-size: 12px; color: #666; margin-bottom: 8px;">验证码预览：</div>
                        <NSpace align="center">
                          <img
                            :src="captchaData.imageBase64"
                            alt="验证码"
                            style="border: 1px solid #d9d9d9; border-radius: 4px; max-height: 60px;"
                          />
                          <div>
                            <div style="font-size: 12px; color: #666;">codeKey: {{ captchaData.codeKey }}</div>
                            <div v-if="captchaData.recognizedCode" style="margin-top: 4px; padding: 6px 12px; background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 4px;">
                              <span style="font-weight: bold; color: #0ea5e9; font-size: 16px;">{{ captchaData.recognizedCode }}</span>
                              <span style="margin-left: 8px; font-size: 12px; color: #18a058;">✓ 识别成功</span>
                            </div>
                            <div v-else-if="captchaData.recognitionMessage" style="margin-top: 4px; padding: 6px 12px; background-color: #fef3f2; border: 1px solid #f97316; border-radius: 4px;">
                              <span style="color: #f97316; font-size: 12px;">{{ captchaData.recognitionMessage }}</span>
                            </div>
                          </div>
                        </NSpace>
                      </div>
                    </NSpace>
                  </NFormItem>
                </template>
              </NForm>

              <!-- 验证码配置保存按钮 -->
              <!-- <div style="text-align: right; margin-top: 16px;">
                <NButton type="primary" @click="handleSaveCaptchaConfig" :loading="modalLoading">
                  保存验证码配置
                </NButton>
              </div> -->
            </template>
          </NTabPane>
        </NTabs>
      </CrudModal>

      <!-- 复制环境 弹窗 -->
      <CrudModal
        v-model:visible="copyModalVisible"
        title="复制环境配置"
        @save="handleCopySubmit"
      >
        <NForm
          label-placement="left"
          label-align="left"
          :label-width="80"
          :model="copyForm"
        >
          <NFormItem label="新环境名称" path="name">
            <NInput v-model:value="copyForm.name" clearable placeholder="请输入新环境名称" />
          </NFormItem>
          <NFormItem label="目标项目" path="project_id">
            <NSelect
              v-model:value="copyForm.project_id"
              :options="projectOption"
              label-field="name"
              value-field="id"
              placeholder="请选择目标项目"
            />
          </NFormItem>
        </NForm>
      </CrudModal>
    </NLayoutContent>
  </NLayout>
</template>

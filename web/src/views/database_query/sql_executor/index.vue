<template>
  <div class="main-container">
    <n-grid :cols="4" :x-gap="12" class="h-full">
      <!-- 左侧数据库结构 -->
      <n-gi :span="1">
        <n-card title="数据库结构" size="small" class="h-full">
          <template #header-extra>
            <n-select
              v-model:value="selectedConnectionId"
              placeholder="选择数据库连接"
              :options="connectionOptions"
              size="small"
              @update:value="handleConnectionChange"
            />
          </template>
          <n-scrollbar style="max-height: calc(100vh - 200px)">
            <n-tree
              v-if="treeData.length > 0"
              :data="treeData"
              :render-label="renderTreeLabel"
              :on-load="handleTreeLoad"
              block-line
              expand-on-click
              @update:selected-keys="handleTableSelect"
            />
            <n-empty v-else description="请选择数据库连接" />
          </n-scrollbar>
        </n-card>
      </n-gi>

      <!-- 右侧SQL执行区域 -->
      <n-gi :span="3">
        <n-card title="SQL执行器" size="small" class="h-full">
          <template #header-extra>
            <n-space>
              <n-input
                v-model:value="queryName"
                placeholder="查询名称(可选)"
                size="small"
                style="width: 150px"
              />
              <n-button
                type="primary"
                size="small"
                :loading="executeLoading"
                :disabled="!selectedConnectionId || !sqlContent.trim()"
                @click="handleExecute"
              >
                执行查询
              </n-button>
              <n-button size="small" @click="handleClear">清空</n-button>
            </n-space>
          </template>

          <n-split direction="vertical" :default-size="0.4">
            <!-- SQL编辑器 -->
            <template #1>
              <div class="sql-editor-container">
                <n-input
                  v-model:value="sqlContent"
                  type="textarea"
                  placeholder="请输入SQL语句..."
                  :autosize="{ minRows: 8, maxRows: 15 }"
                  show-count
                />
              </div>
            </template>

            <!-- 查询结果 -->
            <template #2>
              <div class="result-container">
                <n-tabs v-if="queryResult" type="line" animated>
                  <n-tab-pane name="result" tab="查询结果">
                    <div class="result-info mb-2">
                      <n-space>
                        <n-tag type="info">
                          执行时间: {{ queryResult.execution_time }}ms
                        </n-tag>
                        <n-tag type="success" v-if="queryResult.total_count !== undefined">
                          结果行数: {{ queryResult.total_count }}
                        </n-tag>
                        <n-tag type="warning" v-if="queryResult.affected_rows !== undefined">
                          影响行数: {{ queryResult.affected_rows }}
                        </n-tag>
                      </n-space>
                    </div>
                    
                    <n-data-table
                      v-if="queryResult.columns && queryResult.columns.length > 0"
                      :columns="resultColumns"
                      :data="queryResult.data"
                      :max-height="300"
                      :scroll-x="resultColumns.length * 150"
                      size="small"
                      striped
                    />
                    <n-result
                      v-else
                      status="success"
                      title="执行成功"
                      :description="`影响行数: ${queryResult.affected_rows || 0}`"
                    />
                  </n-tab-pane>
                  
                  <n-tab-pane name="raw" tab="原始数据">
                    <n-code
                      :code="JSON.stringify(queryResult, null, 2)"
                      language="json"
                      :hljs="hljs"
                      style="max-height: 300px; overflow: auto;"
                    />
                  </n-tab-pane>
                </n-tabs>
                
                <n-empty v-else description="暂无查询结果" />
              </div>
            </template>
          </n-split>
        </n-card>
      </n-gi>
    </n-grid>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, h } from 'vue'
import { NIcon, NButton, useMessage } from 'naive-ui'
import { DatabaseOutline, TableOutline } from '@vicons/ionicons5'
import api from '@/api/database_query'
import hljs from 'highlight.js/lib/core'
import json from 'highlight.js/lib/languages/json'

hljs.registerLanguage('json', json)

defineOptions({ name: 'SqlExecutor' })

const $message = useMessage()

// 数据库连接相关
const connections = ref([])
const selectedConnectionId = ref(null)
const connectionOptions = computed(() => 
  connections.value.map(conn => ({
    label: `${conn.name} (${conn.db_type})`,
    value: conn.id
  }))
)

// 数据库结构树
const treeData = ref([])
const tableSchema = ref({})

// SQL执行相关
const sqlContent = ref('')
const queryName = ref('')
const executeLoading = ref(false)
const queryResult = ref(null)

// 查询结果表格列
const resultColumns = computed(() => {
  if (!queryResult.value?.columns) return []
  return queryResult.value.columns.map(col => ({
    title: col,
    key: col,
    width: 150,
    ellipsis: { tooltip: true }
  }))
})

// 获取数据库连接列表
async function getConnections() {
  try {
    const { data } = await api.getConnections({ page_size: 100 })
    connections.value = data.data.filter(conn => conn.is_active)
  } catch (error) {
    console.error('获取连接列表失败:', error)
  }
}

// 处理连接变化
async function handleConnectionChange(connectionId) {
  if (!connectionId) {
    treeData.value = []
    return
  }
  
  try {
    const { data } = await api.getDatabaseSchema(connectionId)
    treeData.value = data.data.tables.map(table => ({
      key: table.table_name,
      label: table.table_name,
      prefix: () => h(NIcon, { component: TableOutline }),
      isLeaf: true,
      table_comment: table.table_comment
    }))
  } catch (error) {
    $message.error('获取数据库结构失败')
    console.error(error)
  }
}

// 渲染树节点标签
function renderTreeLabel({ option }) {
  return [
    option.label,
    option.table_comment && h('span', { style: 'color: #999; margin-left: 8px; font-size: 12px;' }, `(${option.table_comment})`)
  ]
}

// 处理表格选择
async function handleTableSelect(selectedKeys) {
  if (selectedKeys.length === 0) return
  
  const tableName = selectedKeys[0]
  if (!tableSchema.value[tableName]) {
    try {
      const { data } = await api.getTableSchema(selectedConnectionId.value, tableName)
      tableSchema.value[tableName] = data.data
    } catch (error) {
      console.error('获取表结构失败:', error)
    }
  }
  
  // 自动生成SELECT语句
  sqlContent.value = `SELECT * FROM ${tableName} LIMIT 10;`
}

// 执行SQL查询
async function handleExecute() {
  if (!selectedConnectionId.value) {
    $message.warning('请先选择数据库连接')
    return
  }
  
  if (!sqlContent.value.trim()) {
    $message.warning('请输入SQL语句')
    return
  }
  
  try {
    executeLoading.value = true
    const { data } = await api.executeQuery({
      sql_content: sqlContent.value,
      database_connection_id: selectedConnectionId.value,
      query_name: queryName.value || null,
      limit: 1000
    })
    
    queryResult.value = data.data
    $message.success('查询执行成功')
  } catch (error) {
    $message.error(`查询执行失败: ${error.response?.data?.msg || error.message}`)
    console.error(error)
  } finally {
    executeLoading.value = false
  }
}

// 清空内容
function handleClear() {
  sqlContent.value = ''
  queryName.value = ''
  queryResult.value = null
}

// 树节点加载（暂未使用）
function handleTreeLoad() {
  return Promise.resolve()
}

onMounted(() => {
  getConnections()
})
</script>

<style scoped>
.main-container {
  height: calc(100vh - 100px);
}

.sql-editor-container {
  height: 100%;
  padding: 8px 0;
}

.result-container {
  height: 100%;
  padding: 8px 0;
}

.result-info {
  padding: 8px 0;
}

:deep(.n-card__content) {
  height: calc(100% - 46px);
  padding: 12px;
}

:deep(.n-split .n-split-pane) {
  overflow: hidden;
}
</style>

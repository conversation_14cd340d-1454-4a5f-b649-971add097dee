<template>
  <AppPage :show-footer="false">
    <div flex-1>
      <!-- 用户信息卡片 -->
      <n-card rounded-10>
        <div flex items-center justify-between>
          <div flex items-center>
            <!-- 使用用户名头像 -->
            <UserAvatarComponent
              :username="userStore.name"
              :size="60"
              :glow="true"
              :gradient="true"
            />
            <div ml-10>
              <p text-20 font-semibold>
                {{ $t('views.workbench.text_hello', { username: userStore.name }) }}
              </p>
              <p mt-5 text-14 op-60>{{ $t('views.workbench.text_welcome') }}</p>
            </div>
          </div>
          <n-space :size="12" :wrap="false">
            <n-statistic v-for="item in statisticData" :key="item.id" v-bind="item"></n-statistic>
          </n-space>
        </div>
      </n-card>

      <!-- 测试计划统计 -->
      <n-grid :cols="2" :x-gap="16" mt-15>
        <!-- 接口测试计划统计 -->
        <n-grid-item>
          <n-card
            :title="$t('views.workbench.label_api_test_plan')"
            size="small"
            :segmented="true"
            rounded-10
          >
            <n-spin :show="loading">
              <div v-if="apiTestPlanStats.length > 0">
                <div
                  v-for="project in apiTestPlanStats"
                  :key="project.project_id"
                  class="project-card"
                >
                  <div flex items-center justify-between mb-3>
                    <h3 text-lg font-semibold>{{ project.project_name }}</h3>
                    <n-tag :type="project.total_plans > 0 ? 'info' : 'default'" size="small">
                      {{ $t('views.workbench.label_total_plans') }}: {{ project.total_plans }}
                    </n-tag>
                  </div>

                  <n-grid :cols="4" :x-gap="12" mb-3>
                    <n-grid-item>
                      <n-statistic
                        :label="$t('views.workbench.label_executed_plans')"
                        :value="project.executed_plans"
                        :value-style="{ color: '#1890ff' }"
                      />
                    </n-grid-item>
                    <n-grid-item>
                      <n-statistic
                        :label="$t('views.workbench.label_success_plans')"
                        :value="project.success_plans"
                        :value-style="{ color: '#52c41a' }"
                      />
                    </n-grid-item>
                    <n-grid-item>
                      <n-statistic
                        :label="$t('views.workbench.label_failed_plans')"
                        :value="project.failed_plans"
                        :value-style="{ color: '#ff4d4f' }"
                      />
                    </n-grid-item>
                    <n-grid-item>
                      <n-statistic
                        :label="$t('views.workbench.label_avg_pass_rate')"
                        :value="project.avg_pass_rate + '%'"
                        :value-style="{ color: project.avg_pass_rate >= 80 ? '#52c41a' : project.avg_pass_rate >= 60 ? '#faad14' : '#ff4d4f' }"
                      />
                    </n-grid-item>
                  </n-grid>

                  <div text-sm op-60>
                    {{ $t('views.workbench.label_last_execution') }}:
                    {{ project.last_execution || $t('views.workbench.text_no_execution') }}
                  </div>
                </div>
              </div>
              <div v-else text-center py-8 op-60>
                暂无接口测试计划数据
              </div>
            </n-spin>
          </n-card>
        </n-grid-item>

        <!-- 功能测试计划统计 -->
        <n-grid-item>
          <n-card
            :title="$t('views.workbench.label_functional_test_plan')"
            size="small"
            :segmented="true"
            rounded-10
          >
            <div text-center py-16 op-40>
              <n-icon size="48" mb-4>
                <icon-mdi-construction />
              </n-icon>
              <p text-lg>{{ $t('views.workbench.text_coming_soon') }}</p>
            </div>
          </n-card>
        </n-grid-item>
      </n-grid>
    </div>
  </AppPage>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useUserStore } from '@/store'
import { useI18n } from 'vue-i18n'
import apiTestPlanApi from '@/api/apiTestPlan'
import UserAvatarComponent from '@/components/common/UserAvatar.vue'

const { t } = useI18n({ useScope: 'global' })
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const apiTestPlanStats = ref([])

// 统计数据
const statisticData = computed(() => {
  const totalProjects = apiTestPlanStats.value.length
  const totalPlans = apiTestPlanStats.value.reduce((sum, project) => sum + project.total_plans, 0)
  const totalExecuted = apiTestPlanStats.value.reduce((sum, project) => sum + project.executed_plans, 0)

  return [
    {
      id: 0,
      label: t('views.workbench.label_number_of_items'),
      value: totalProjects.toString(),
    },
    {
      id: 1,
      label: t('views.workbench.label_total_plans'),
      value: totalPlans.toString(),
    },
    {
      id: 2,
      label: t('views.workbench.label_executed_plans'),
      value: totalExecuted.toString(),
    },
  ]
})



// 获取接口测试计划统计数据
const fetchApiTestPlanStats = async () => {
  try {
    loading.value = true
    const response = await apiTestPlanApi.getApiTestPlanStatistics()
    if (response.code === 200) {
      apiTestPlanStats.value = response.data || []
    }
  } catch (error) {
    console.error('获取接口测试计划统计失败:', error)
    $message.error('获取统计数据失败')
  } finally {
    loading.value = false
  }
}

// 页面挂载时获取数据
onMounted(() => {
  fetchApiTestPlanStats()
})
</script>

<style scoped>
.project-card {
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.project-card:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}
</style>

<template>
  <CommonPage show-footer title="提示词模板管理">
    <template #action>
      <NButton v-permission="'post/api/v1/prompt_template/create'" type="primary" @click="handleAdd">
        <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建模板
      </NButton>
    </template>

    <!-- 表格 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="api.getPromptTemplateList"
    >
      <template #queryBar>
        <QueryBarItem label="模板名称" :label-width="80">
          <NInput
            v-model:value="queryItems.name"
            type="text"
            placeholder="请输入模板名称"
            clearable
          />
        </QueryBarItem>
        <QueryBarItem label="模板分类" :label-width="80">
          <NSelect
            v-model:value="queryItems.category"
            placeholder="请选择模板分类"
            style="width: 150px;"
            clearable
            :options="categoryOptions"
          />
        </QueryBarItem>
        <QueryBarItem label="状态" :label-width="50">
          <NSelect
            v-model:value="queryItems.is_active"
            placeholder="请选择状态"
            style="width: 130px;"
            clearable
            :options="statusOptions"
          />
        </QueryBarItem>
      </template>
    </CrudTable>

    <!-- 新增/编辑弹窗 -->
    <CrudModal
      v-model:visible="modalVisible"
      :title="modalTitle"
      :loading="modalLoading"
      @save="handleSave"
    >
      <NForm
        ref="modalFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="modalForm"
        :rules="modalRules"
      >
        <NFormItem label="模板名称" path="name">
          <NInput v-model:value="modalForm.name" placeholder="请输入模板名称" />
        </NFormItem>
        <NFormItem label="模板分类" path="category">
          <NInput v-model:value="modalForm.category" placeholder="请输入模板分类" />
        </NFormItem>
        <NFormItem label="模板描述" path="description">
          <NInput
            v-model:value="modalForm.description"
            type="textarea"
            placeholder="请输入模板描述"
            :rows="3"
          />
        </NFormItem>
        <NFormItem label="提示词内容" path="prompt_content">
          <NInput
            v-model:value="modalForm.prompt_content"
            type="textarea"
            placeholder="请输入提示词内容，支持Jinja2模板语法"
            :rows="10"
          />
        </NFormItem>
        <NFormItem label="变量定义" path="variables">
          <NInput
            v-model:value="modalForm.variables_json"
            type="textarea"
            placeholder="请输入变量定义（JSON格式）"
            :rows="5"
          />
        </NFormItem>
        <NFormItem label="是否启用" path="is_active">
          <NSwitch v-model:value="modalForm.is_active" />
        </NFormItem>
        <NFormItem label="是否默认" path="is_default">
          <NSwitch v-model:value="modalForm.is_default" />
        </NFormItem>
      </NForm>
    </CrudModal>

    <!-- 测试弹窗 -->
    <NModal v-model:show="testModalVisible" preset="dialog" title="测试提示词模板" style="width: 800px">
      <NForm
        ref="testFormRef"
        label-placement="left"
        label-align="left"
        :label-width="100"
        :model="testForm"
      >
        <NFormItem label="变量值" path="variables">
          <NInput
            v-model:value="testForm.variables_json"
            type="textarea"
            placeholder="请输入变量值（JSON格式）"
            :rows="5"
          />
        </NFormItem>
        <NFormItem label="AI模型" path="ai_model_config_id">
          <NSelect
            v-model:value="testForm.ai_model_config_id"
            placeholder="请选择AI模型（不选择则使用默认模型）"
            clearable
            :options="aiModelOptions"
          />
        </NFormItem>
      </NForm>
      
      <template #action>
        <NSpace>
          <NButton @click="testModalVisible = false">取消</NButton>
          <NButton type="primary" :loading="testLoading" @click="handleTest">测试</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 测试结果弹窗 -->
    <NModal v-model:show="testResultModalVisible" preset="dialog" title="测试结果" style="width: 900px">
      <NSpace vertical>
        <NCard title="渲染后的提示词" size="small">
          <NInput
            :value="testResult.rendered_prompt"
            type="textarea"
            readonly
            :rows="8"
          />
        </NCard>
        <NCard v-if="testResult.ai_response" title="AI响应" size="small">
          <NInput
            :value="testResult.ai_response"
            type="textarea"
            readonly
            :rows="10"
          />
        </NCard>
        <NCard v-if="testResult.error_message" title="错误信息" size="small">
          <NText type="error">{{ testResult.error_message }}</NText>
        </NCard>
        <NCard v-if="testResult.response_time" title="响应时间" size="small">
          <NText>{{ testResult.response_time.toFixed(2) }}秒</NText>
        </NCard>
      </NSpace>
      
      <template #action>
        <NButton @click="testResultModalVisible = false">关闭</NButton>
      </template>
    </NModal>
  </CommonPage>
</template>

<script setup>
import { h, onMounted, ref } from 'vue'
import { NButton, NTag, NSwitch, useMessage } from 'naive-ui'
import { formatDateTime, renderIcon } from '@/utils'
import api from '@/api/prompt_template'
import aiModelApi from '@/api/ai_model_config'

defineOptions({ name: 'PromptTemplate' })

const $message = useMessage()
const $table = ref(null)
const modalVisible = ref(false)
const modalLoading = ref(false)
const modalTitle = ref('')
const modalFormRef = ref(null)
const testModalVisible = ref(false)
const testResultModalVisible = ref(false)
const testLoading = ref(false)

// 查询参数
const queryItems = ref({
  name: null,
  category: null,
  is_active: null
})

// 表格列定义
const columns = [
  { title: 'ID', key: 'id', width: 60, ellipsis: { tooltip: true } },
  { title: '模板名称', key: 'name', width: 150, ellipsis: { tooltip: true } },
  { title: '分类', key: 'category', width: 120, ellipsis: { tooltip: true } },
  { title: '描述', key: 'description', width: 200, ellipsis: { tooltip: true } },
  {
    title: '状态',
    key: 'is_active',
    width: 80,
    render: (row) => h(NTag, { type: row.is_active ? 'success' : 'error' }, 
      { default: () => row.is_active ? '启用' : '禁用' })
  },
  {
    title: '默认',
    key: 'is_default',
    width: 80,
    render: (row) => h(NTag, { type: row.is_default ? 'info' : 'default' }, 
      { default: () => row.is_default ? '是' : '否' })
  },
  { title: '使用次数', key: 'usage_count', width: 100 },
  { title: '创建时间', key: 'created_at', width: 180, render: (row) => formatDateTime(row.created_at) },
  {
    title: '操作',
    key: 'actions',
    width: 280,
    align: 'center',
    fixed: 'right',
    hideInExcel: true,
    render: (row) => {
      return [
        h(
          NButton,
          { size: 'small', type: 'primary', style: 'margin-right: 8px;', onClick: () => handleEdit(row) },
          { default: () => '编辑', icon: renderIcon('material-symbols:edit-outline', { size: 14 }) }
        ),
        // h(
        //   NButton,
        //   { size: 'small', type: 'info', style: 'margin-right: 8px;', onClick: () => handleTestTemplate(row) },
        //   { default: () => '测试', icon: renderIcon('material-symbols:play-arrow', { size: 14 }) }
        // ),
        // h(
        //   NButton,
        //   { size: 'small', type: 'warning', style: 'margin-right: 8px;', onClick: () => handleCopy(row) },
        //   { default: () => '复制', icon: renderIcon('material-symbols:content-copy', { size: 14 }) }
        // ),
        h(
          NButton,
          { size: 'small', type: 'error', onClick: () => handleDelete(row.id) },
          { default: () => '删除', icon: renderIcon('material-symbols:delete-outline', { size: 14 }) }
        )
      ]
    }
  }
]

// 表单数据
const modalForm = ref({
  id: null,
  name: '',
  category: '',
  description: '',
  prompt_content: '',
  variables_json: '',
  is_active: true,
  is_default: false
})

// 表单验证规则
const modalRules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  category: [{ required: true, message: '请输入模板分类', trigger: 'blur' }],
  prompt_content: [{ required: true, message: '请输入提示词内容', trigger: 'blur' }]
}

// 测试表单
const testForm = ref({
  template_id: null,
  variables_json: '{}',
  ai_model_config_id: null
})

// 测试结果
const testResult = ref({
  success: false,
  rendered_prompt: '',
  ai_response: '',
  error_message: '',
  response_time: 0
})

// 选项数据
const categoryOptions = ref([])
const statusOptions = ref([
  { label: '启用', value: true },
  { label: '禁用', value: false }
])
const aiModelOptions = ref([])

// 初始化
onMounted(async () => {
  await loadCategories()
  await loadAiModels()
})

// 加载分类选项
async function loadCategories() {
  try {
    const { data } = await api.getPromptTemplateCategories()
    categoryOptions.value = data.map(category => ({ label: category, value: category }))
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// 加载AI模型选项
async function loadAiModels() {
  try {
    const { data } = await aiModelApi.getAiModelConfigList({ page: 1, page_size: 100 })
    aiModelOptions.value = data.data.map(model => ({ 
      label: model.name, 
      value: model.id 
    }))
  } catch (error) {
    console.error('加载AI模型失败:', error)
  }
}

// 新增
function handleAdd() {
  modalTitle.value = '新增提示词模板'
  modalForm.value = {
    id: null,
    name: '',
    category: '',
    description: '',
    prompt_content: '',
    variables_json: '{}',
    is_active: true,
    is_default: false
  }
  modalVisible.value = true
}

// 编辑
function handleEdit(row) {
  modalTitle.value = '编辑提示词模板'
  modalForm.value = {
    id: row.id,
    name: row.name,
    category: row.category,
    description: row.description,
    prompt_content: row.prompt_content,
    variables_json: JSON.stringify(row.variables || {}, null, 2),
    is_active: row.is_active,
    is_default: row.is_default
  }
  modalVisible.value = true
}

// 保存
async function handleSave() {
  try {
    await modalFormRef.value?.validate()
    modalLoading.value = true
    
    const formData = { ...modalForm.value }
    
    // 解析JSON字符串
    try {
      formData.variables = JSON.parse(formData.variables_json || '{}')
    } catch (error) {
      $message.error('变量定义JSON格式错误')
      return
    }
    
    delete formData.variables_json
    
    if (formData.id) {
      await api.updatePromptTemplate(formData)
      $message.success('更新成功')
    } else {
      delete formData.id
      await api.createPromptTemplate(formData)
      $message.success('创建成功')
    }
    
    modalVisible.value = false
    $table.value?.handleSearch()
    await loadCategories() // 重新加载分类
  } catch (error) {
    console.error('保存失败:', error)
  } finally {
    modalLoading.value = false
  }
}

// 删除
async function handleDelete(id) {
  try {
    await api.deletePromptTemplate(id)
    $message.success('删除成功')
    $table.value?.handleSearch()
  } catch (error) {
    console.error('删除失败:', error)
  }
}

// 复制
async function handleCopy(row) {
  try {
    const newName = `${row.name}_副本`
    await api.copyPromptTemplate({ id: row.id, name: newName })
    $message.success('复制成功')
    $table.value?.handleSearch()
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 测试模板
function handleTestTemplate(row) {
  testForm.value = {
    template_id: row.id,
    variables_json: JSON.stringify(row.variables || {}, null, 2),
    ai_model_config_id: null
  }
  testModalVisible.value = true
}

// 执行测试
async function handleTest() {
  try {
    testLoading.value = true
    
    const formData = { ...testForm.value }
    
    // 解析JSON字符串
    try {
      formData.variables = JSON.parse(formData.variables_json || '{}')
    } catch (error) {
      $message.error('变量值JSON格式错误')
      return
    }
    
    delete formData.variables_json
    
    const { data } = await api.testPromptTemplate(formData)
    testResult.value = data
    testModalVisible.value = false
    testResultModalVisible.value = true
    
    if (data.success) {
      $message.success('测试成功')
    } else {
      $message.error('测试失败')
    }
  } catch (error) {
    console.error('测试失败:', error)
    $message.error('测试失败')
  } finally {
    testLoading.value = false
  }
}
</script>

<script setup>
import { ref, reactive, computed, onMounted, h } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage, useDialog } from 'naive-ui'
import {
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NTree,
  NButton,
  NInput,
  NSelect,
  NDatePicker,
  NTag,
  NSpace,
  NDropdown,
  NPopconfirm,
  NTooltip,
  NForm,
  NFormItem
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import apiTestPlanApi from '@/api/apiTestPlan'
import projectApi from '@/api/project'
import environmentApi from '@/api/environment'
import TheIcon from '@/components/icon/TheIcon.vue'
import { lStorage } from '@/utils'
import { useUserStore } from '@/store'

defineOptions({ name: '接口测试计划' })

const $message = useMessage()
const $dialog = useDialog()
const $table = ref(null)
const userStore = useUserStore()
const router = useRouter()

// 项目相关
const projectOption = ref([])
const selectedKeys = ref([])
const selectedProjectId = ref(null)
const selectedProjectName = ref('')

// 环境相关
const environmentOptions = ref([])
const selectedEnvironment = ref(null)

// 查询条件
const queryItems = reactive({
  plan_name: null,
  status: null,
  level: null,
  project_id: ''
})

// 状态选项
const statusOptions = [
  { label: '未开始', value: 'not_started' },
  { label: '进行中', value: 'in_progress' },
  { label: '已完成', value: 'completed' }
]

// 等级选项
const levelOptions = [
  { label: '高', value: 'high' },
  { label: '中', value: 'medium' },
  { label: '低', value: 'low' }
]

// 获取项目列表
const getProjectList = async () => {
  try {
    const { data } = await projectApi.getProjectList({ page: 1, page_size: 1000 })
    projectOption.value = data || []

    // 默认选择第一个项目
    if (projectOption.value.length > 0) {
      const firstProject = projectOption.value[0]
      selectedKeys.value = [firstProject.id]
      selectedProjectId.value = firstProject.id
      selectedProjectName.value = firstProject.name
      queryItems.project_id = firstProject.id

      // 获取该项目的环境配置
      await getEnvironmentList(firstProject.id)

      // 刷新表格
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

// 获取环境列表
const getEnvironmentList = async (projectId) => {
  try {
    console.log('正在获取项目环境列表，项目ID:', projectId)
    const { data } = await environmentApi.getEnvironmentList({
      project_id: projectId,
      page: 1,
      page_size: 1000
    })

    console.log('获取到的环境数据:', data)
    environmentOptions.value = (data || []).map(env => ({
      label: env.name,  // 使用环境名称作为显示标签
      value: env.id,
      key: env.id
    }))

    console.log('处理后的环境选项:', environmentOptions.value)

    // 恢复用户选择的环境
    const savedEnv = lStorage.get(`selected_environment_${projectId}_${userStore.userInfo.id}`)
    if (savedEnv && environmentOptions.value.find(opt => opt.value === savedEnv)) {
      selectedEnvironment.value = savedEnv
    } else if (environmentOptions.value.length > 0) {
      selectedEnvironment.value = environmentOptions.value[0].value
    }

    console.log('选中的环境:', selectedEnvironment.value)
  } catch (error) {
    console.error('获取环境列表失败:', error)
    environmentOptions.value = []
  }
}

// 项目树节点属性
const nodeProps = ({ option }) => {
  return {
    onClick() {
      selectedKeys.value = [option.id]
      selectedProjectId.value = option.id
      selectedProjectName.value = option.name
      queryItems.project_id = option.id

      // 获取该项目的环境配置
      getEnvironmentList(option.id)

      // 刷新表格
      $table.value?.handleSearch()
    }
  }
}

// 环境选择处理
const handleEnvironmentSelect = (envId) => {
  selectedEnvironment.value = envId
  // 保存用户选择
  lStorage.set(`selected_environment_${selectedProjectId.value}_${userStore.userInfo.id}`, envId)
}

// 表格列配置
const columns = [
  {
    title: '计划名称',
    key: 'plan_name',
    width: 188,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '等级',
    key: 'level',
    width: 60,
    render: (row) => {
      const levelMap = {
        'high': { text: '高', type: 'error' },
        'medium': { text: '中', type: 'warning' },
        'low': { text: '低', type: 'info' }
      }
      const level = levelMap[row.level] || { text: row.level, type: 'default' }
      return h(NTag, { type: level.type, size: 'small' }, { default: () => level.text })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 80,
    render: (row) => {
      const statusMap = {
        'not_started': { text: '未开始', type: 'default' },
        'in_progress': { text: '进行中', type: 'info' },
        'completed': { text: '已完成', type: 'success' }
      }
      const status = statusMap[row.status] || { text: row.status, type: 'default' }
      return h(NTag, { type: status.type, size: 'small' }, { default: () => status.text })
    }
  },
  {
    title: '运行环境',
    key: 'environment_name',
    width: 180,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '执行结果',
    key: 'execution_result',
    width: 100,
    render: (row) => {
      if (!row.execution_result) return '-'
      const resultMap = {
        'success': { text: '成功', type: 'success' },
        'failed': { text: '失败', type: 'error' },
        'running': { text: '执行中', type: 'info' }
      }
      const result = resultMap[row.execution_result] || { text: row.execution_result, type: 'default' }
      return h(NTag, { type: result.type, size: 'small' }, { default: () => result.text })
    }
  },
  {
    title: '通过率',
    key: 'pass_rate',
    width: 80,
    render: (row) => {
      if (row.pass_rate === null || row.pass_rate === undefined) return '-'
      return `${row.pass_rate}%`
    }
  },
  {
    title: '最近执行时间',
    key: 'last_execution_time',
    width: 180,
    render: (row) => {
      return row.last_execution_time ? formatDate(row.last_execution_time) : '-'
    }
  },
  {
    title: '创建人',
    key: 'creator_name',
    width: 100
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render: (row) => formatDate(row.created_at)
  },
  {
    title: '操作',
    key: 'actions',
    width: 210,
    align: 'center',
    fixed: 'right',
    render: (row) => {
      return h(NSpace, { size: 'small' }, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleViewDetail(row)
          }, { default: () => '详情' }),
          // h(NButton, {
          //   size: 'small',
          //   type: 'info',
          //   secondary: true,
          //   onClick: () => handleExecute(row)
          // }, { default: () => '执行' }),
          h(NButton, {
            size: 'small',
            type: 'primary',
            onClick: () => handleEdit(row)
          }, { default: () => '编辑' }),
          // h(NButton, {
          //   size: 'small',
          //   type: 'warning',
          //   secondary: true,
          //   onClick: () => handleCopy(row)
          // }, { default: () => '复制' }),
          h(NPopconfirm, {
            onPositiveClick: () => handleDelete(row)
          }, {
            default: () => '确定删除吗？',
            trigger: () => h(NButton, {
              size: 'small',
              type: 'error',
              secondary: true
            }, { default: () => '删除' })
          })
        ]
      })
    }
  }
]

// CRUD配置
const {
  modalVisible,
  modalTitle,
  modalAction,
  modalLoading,
  handleSave: originalHandleSave,
  modalForm,
  modalFormRef,
  handleEdit: originalHandleEdit,
  handleDelete,
  handleAdd: originalHandleAdd,
} = useCRUD({
  name: '测试计划',
  initForm: {
    plan_name: '',
    level: 'medium',
    status: 'not_started',
    description: '',
    project_id: null,
    environment_id: null,
  },
  doCreate: apiTestPlanApi.createApiTestPlan,
  doUpdate: apiTestPlanApi.updateApiTestPlan,
  doDelete: apiTestPlanApi.deleteApiTestPlan,
  refresh: () => $table.value?.handleSearch(),
})

// 表单规则
const modalFormRules = {
  plan_name: [
    { required: true, message: '请输入计划名称', trigger: 'blur' },
    { min: 1, max: 100, message: '计划名称长度在1到100个字符', trigger: 'blur' }
  ],
  level: [
    { required: true, message: '请选择等级', trigger: 'change' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ],
  project_id: [
    { required: true, message: '请选择项目', trigger: 'change' }
  ]
}

// 重写新增方法
const handleAdd = async () => {
  if (!selectedProjectId.value) {
    $message.warning('请先选择项目')
    return
  }

  // 确保环境选项已加载
  if (environmentOptions.value.length === 0) {
    await getEnvironmentList(selectedProjectId.value)
  }

  // 先调用原始的新增方法来重置表单
  originalHandleAdd()

  // 然后设置项目相关的值
  modalForm.value.project_id = selectedProjectId.value
  modalForm.value.environment_id = selectedEnvironment.value || null
}

// 重写编辑方法
const handleEdit = async (row) => {
  originalHandleEdit(row)

  // 如果编辑的项目与当前选中的项目不同，需要重新加载环境选项
  if (row.project_id !== selectedProjectId.value) {
    await getEnvironmentList(row.project_id)
  }
}

// 重写保存方法
const handleSave = async () => {
  try {
    // 确保数据类型正确
    if (modalForm.value.project_id) {
      modalForm.value.project_id = Number(modalForm.value.project_id)
    }
    if (modalForm.value.environment_id) {
      modalForm.value.environment_id = Number(modalForm.value.environment_id)
    }

    await originalHandleSave()
  } catch (error) {
    console.error('保存失败:', error)
    $message.error(`保存失败: ${error.message || '未知错误'}`)
  }
}

// 查看详情
const handleViewDetail = (row) => {
  router.push(`/test_plan/api_test_plan/detail/${row.id}`)
}

// 执行测试计划
const handleExecute = async (row) => {
  if (!selectedEnvironment.value) {
    $message.warning('请先选择运行环境')
    return
  }

  try {
    $message.loading('正在执行测试计划...', { duration: 0 })
    const result = await apiTestPlanApi.executeApiTestPlan({
      plan_id: row.id,
      environment_id: selectedEnvironment.value
    })
    $message.destroyAll()
    $message.success('测试计划执行成功')
    $table.value?.handleSearch()
  } catch (error) {
    $message.destroyAll()
    $message.error(`执行失败: ${error.message || '未知错误'}`)
  }
}

// 复制测试计划
const handleCopy = async (row) => {
  try {
    await apiTestPlanApi.copyApiTestPlan({ id: row.id })
    $message.success('复制成功')
    $table.value?.handleSearch()
  } catch (error) {
    $message.error(`复制失败: ${error.message || '未知错误'}`)
  }
}

// 重置查询
const handleReset = () => {
  queryItems.plan_name = ''
  queryItems.status = ''
  queryItems.level = ''
  if (selectedProjectId.value) {
    queryItems.project_id = selectedProjectId.value
  }
}

// 组件挂载
onMounted(() => {
  getProjectList()
})
</script>

<template>
  <NLayout has-sider wh-full>
    <NLayoutSider
      bordered
      content-style="padding: 24px;"
      :collapsed-width="0"
      :width="240"
      show-trigger="arrow-circle"
    >
      <h1>项目列表</h1>
      <br />
      <NTree
        block-line
        :data="projectOption"
        key-field="id"
        label-field="name"
        default-expand-all
        :node-props="nodeProps"
        :selected-keys="selectedKeys"
      >
      </NTree>
    </NLayoutSider>
    <NLayoutContent>
      <CommonPage show-footer :title="selectedProjectName ? `${selectedProjectName} - 接口测试计划` : '接口测试计划'">
        <template #action>
          <div style="display: flex; gap: 12px; align-items: center;">
            <!-- 新建测试计划按钮 -->
            <NButton
              v-if="selectedProjectId"
              type="primary"
              @click="handleAdd"
            >
              <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建测试计划
            </NButton>
          </div>
        </template>
        <!-- 表格 -->
        <CrudTable
          ref="$table"
          v-model:query-items="queryItems"
          :columns="columns"
          :get-data="apiTestPlanApi.getApiTestPlanList"
          @reset="handleReset"
        >
          <template #queryBar>
            <QueryBarItem label="" :label-width="80">
              <NInput
                v-model:value="queryItems.plan_name"
                clearable
                type="text"
                placeholder="请输入计划名称"
                @keypress.enter="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="80">
              <NSelect
                v-model:value="queryItems.status"
                clearable
                :options="statusOptions"
                placeholder="状态"
                style="width: 120px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="80">
              <NSelect
                v-model:value="queryItems.level"
                clearable
                :options="levelOptions"
                placeholder="等级"
                style="width: 100px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
          </template>
        </CrudTable>
      </CommonPage>
    </NLayoutContent>
  </NLayout>

  <!-- 新增/编辑模态框 -->
  <CrudModal
    v-model:visible="modalVisible"
    :title="modalTitle"
    :loading="modalLoading"
    :show-footer="true"
    @save="handleSave"
  >
    <NForm
      ref="modalFormRef"
      :model="modalForm"
      :rules="modalFormRules"
      label-placement="left"
      label-width="100px"
      require-mark-placement="right-hanging"
    >
      <NFormItem label="计划名称" path="plan_name">
        <NInput
          v-model:value="modalForm.plan_name"
          placeholder="请输入计划名称"
          clearable
        />
      </NFormItem>
      <NFormItem label="等级" path="level">
        <NSelect
          v-model:value="modalForm.level"
          :options="levelOptions"
          placeholder="请选择等级"
        />
      </NFormItem>
      <NFormItem label="状态" path="status">
        <NSelect
          v-model:value="modalForm.status"
          :options="statusOptions"
          placeholder="请选择状态"
        />
      </NFormItem>
      <NFormItem label="运行环境" path="environment_id">
        <NSelect
          v-model:value="modalForm.environment_id"
          :options="environmentOptions"
          placeholder="请选择运行环境"
          clearable
        />
      </NFormItem>
      <NFormItem label="描述" path="description">
        <NInput
          v-model:value="modalForm.description"
          type="textarea"
          placeholder="请输入描述"
          :rows="4"
          clearable
        />
      </NFormItem>
    </NForm>
  </CrudModal>
</template>

<style scoped>
.mr-5 {
  margin-right: 5px;
}
</style>
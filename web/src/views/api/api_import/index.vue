<template>
  <CommonPage show-footer title="API导入管理">
    <template #action>
      <div class="flex-y-center gap-16px">
        <NButton type="primary" @click="openImportModal">
          <TheIcon icon="material-symbols:add" class="mr-1"/>
          导入接口
        </NButton>
      </div>
    </template>

    <!-- 使用CrudTable组件 -->
    <CrudTable
      ref="$table"
      v-model:query-items="queryItems"
      :columns="columns"
      :get-data="getApiImportList"
      @reset="handleReset"

    >
      <template #queryBar>
        <QueryBarItem label="" :label-width="80">
          <NInput
            v-model:value="queryItems.api_name"
            clearable
            type="text"
            placeholder="请输入接口名称"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="" :label-width="80">
          <NInput
            v-model:value="queryItems.url_path"
            clearable
            type="text"
            placeholder="请输入URL路径"
            @keypress.enter="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="" :label-width="80">
          <NSelect
            v-model:value="queryItems.method"
            clearable
            :options="methodOptions"
            placeholder="请求方式"
            style="width: 120px"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
        <QueryBarItem label="" :label-width="80">
          <NSelect
            v-model:value="queryItems.project_id"
            clearable
            :options="projectOptions"
            placeholder="所属项目"
            style="width: 150px"
            @update:value="$table?.handleSearch()"
          />
        </QueryBarItem>
      </template>

      <!-- 在搜索按钮右边添加批量删除按钮 -->
      <template #actions>
        <NButton
          type="error"
          :disabled="selectedCount === 0"
          @click="handleBatchDelete"
        >
          <TheIcon icon="material-symbols:delete" class="mr-1"/>
          批量删除
        </NButton>
      </template>
    </CrudTable>

    <!-- 导入弹窗 -->
    <n-modal v-model:show="showModal" preset="card" title="导入Swagger/OpenAPI接口" style="width: 800px;">
      <n-form ref="formRef" :model="formData" label-placement="left" label-width="100px" :rules="formRules">
        <n-form-item label="所属项目" path="project_id">
          <n-select
            v-model:value="formData.project_id"
            :options="projectOptions"
            placeholder="请选择所属项目"
            clearable
          />
        </n-form-item>

        <n-form-item label="Swagger文件">
          <n-space vertical style="width: 100%;">
            <n-upload
              accept=".json"
              :max="1"
              :default-upload="false"
              @change="handleFileChange"
              :file-list="fileList"
              @remove="handleFileRemove"
            >
              <n-upload-dragger>
                <div style="margin-bottom: 12px;">
                  <n-icon size="48" :depth="3">
                    <component :is="renderIcon('mdi:cloud-upload-outline')" />
                  </n-icon>
                </div>
                <n-text style="font-size: 16px;">
                  点击或者拖动文件到该区域来上传
                </n-text>
                <n-p depth="3" style="margin: 8px 0 0 0;">
                  支持 Swagger/OpenAPI JSON 格式文件，文件大小不超过 10MB
                </n-p>
              </n-upload-dragger>
            </n-upload>

            <n-alert v-if="parseResult" :type="parseResult.success ? 'success' : 'error'" :title="parseResult.title">
              {{ parseResult.message }}
              <template v-if="parseResult.success && parseResult.count" #footer>
                <n-space>
                  <n-tag type="info">共解析到 {{ parseResult.count }} 个接口</n-tag>
                  <n-tag type="success">文件大小: {{ parseResult.fileSize }}</n-tag>
                </n-space>
              </template>
            </n-alert>
          </n-space>
        </n-form-item>
      </n-form>

      <template #footer>
        <n-space justify="end">
          <n-button @click="showModal = false">取消</n-button>
          <n-button
            type="primary"
            :loading="isSubmitting"
            :disabled="!formData.fileContent || !formData.project_id"
            @click="handleSubmit"
          >
            <template #icon>
              <component :is="renderIcon('mdi:upload')" />
            </template>
            立即导入
          </n-button>
        </n-space>
      </template>
    </n-modal>
    <!-- 编辑弹窗 -->

    <NModal v-model:show="editModalVisible" preset="card" title="编辑接口" style="width: 700px">
      <NForm ref="editFormRef" :model="editeFormData" label-placement="left" label-width="80">
        <NFormItem label="接口名称" required>
          <NInput v-model:value="editeFormData.api_name" placeholder="请输入接口名称"/>
        </NFormItem>

        <NFormItem label="请求方式" required>
          <NSelect v-model:value="editeFormData.method" :options="methodOptions" placeholder="请选择请求方式"/>
        </NFormItem>

        <NFormItem label="URL路径" required>
          <NInput v-model:value="editeFormData.url_path" placeholder="请输入URL路径"/>
        </NFormItem>

        <NFormItem label="请求参数">
          <NInput v-model:value="editeFormData.params_list" type="textarea" placeholder="请输入请求参数"/>
        </NFormItem>

        <NFormItem label="状态" required>
          <NSelect v-model:value="editeFormData.status" :options="statusOptions" placeholder="请选择状态"/>
        </NFormItem>

        <NFormItem label="所属项目" required>
          <NSelect v-model:value="editeFormData.project_id" :options="projectOptions" placeholder="请选择所属项目"
                   clearable/>
        </NFormItem>
      </NForm>

      <template #footer>
        <div class="flex justify-end gap-2">
          <NButton @click="editModalVisible = false">取消</NButton>
          <NButton type="primary" :loading="isSubmitting" @click="editeSubmit">确定</NButton>
        </div>
      </template>
    </NModal>

    <!-- AI生成测试用例弹窗 -->
    <NModal
      v-model:show="aiGenerateModalVisible"
      preset="dialog"
      title="AI生成接口测试用例"
      style="width: 1200px"
    >
      <div v-if="currentApiImport">
        <!-- 接口信息展示 -->
<!--        <NCard title="接口信息" class="mb-4">-->
<!--          <div class="grid grid-cols-2 gap-4">-->
<!--            <div><strong>接口名称：</strong>{{ currentApiImport.api_name }}</div>-->
<!--            <div><strong>请求方法：</strong>{{ currentApiImport.method }}</div>-->
<!--            <div><strong>接口路径：</strong>{{ currentApiImport.url_path }}</div>-->
<!--            <div><strong>请求参数：</strong>{{ currentApiImport.params_list || '无' }}</div>-->
<!--          </div>-->
<!--        </NCard>-->

        <!-- 生成配置 -->
        <NCard title="生成配置" class="mb-4">
          <NForm :model="aiGenerateForm" label-placement="left" label-width="120px">
            <div class="grid grid-cols-3 gap-4">
              <NFormItem label="生成数量">
                <NInputNumber
                  v-model:value="aiGenerateForm.generate_count"
                  :min="1"
                  :max="10"
                  style="width: 100%"
                />
              </NFormItem>
              <NFormItem label="提示词模板">
                <NSelect
                  v-model:value="aiGenerateForm.prompt_template_id"
                  :options="promptTemplateOptions"
                  placeholder="使用默认模板"
                  clearable
                  style="width: 100%"
                />
              </NFormItem>
              <NFormItem label="AI模型">
                <NSelect
                  v-model:value="aiGenerateForm.ai_model_config_id"
                  :options="aiModelOptions"
                  placeholder="使用默认模型"
                  clearable
                  style="width: 100%"
                />
              </NFormItem>
            </div>

            <!-- 保存配置 -->
            <div class="grid grid-cols-2 gap-4 mt-4">
              <NFormItem label="所属项目">
                <NSelect
                  v-model:value="aiGenerateForm.project_id"
                  :options="projectOptions"
                  placeholder="请选择项目"
                  style="width: 100%"
                  @update:value="handleProjectChange"
                />
              </NFormItem>
              <NFormItem label="所属模块">
                <NSelect
                  v-model:value="aiGenerateForm.module_id"
                  :options="moduleOptions"
                  placeholder="请选择模块"
                  clearable
                  style="width: 100%"
                />
              </NFormItem>
            </div>
          </NForm>

          <div class="flex justify-center">
            <NButton
              type="primary"
              :loading="aiGenerating"
              @click="executeAIGenerate"
              :disabled="!currentApiImport"
            >
              <TheIcon icon="material-symbols:auto-awesome-outline" class="mr-1" />
              {{ aiGenerating ? '生成中...' : '开始生成' }}
            </NButton>
          </div>
        </NCard>

        <!-- 生成结果 -->
        <NCard v-if="generatedCases.length > 0" title="生成结果" class="mb-4">
          <div class="flex justify-between items-center mb-4">
            <span>共生成 {{ generatedCases.length }} 个测试用例，已选择 {{ selectedCases.length }} 个</span>
            <div class="flex gap-2">
              <NButton @click="toggleSelectAll">
                {{ selectedCases.length === generatedCases.length ? '取消全选' : '全选' }}
              </NButton>
              <NButton
                type="primary"
                @click="saveSelectedCases"
                :disabled="selectedCases.length === 0 || !aiGenerateForm.project_id"
              >
                <TheIcon icon="material-symbols:save" class="mr-1" />
                保存选中用例 ({{ selectedCases.length }})
              </NButton>
            </div>
          </div>

          <!-- 使用表格展示生成结果 -->
          <NDataTable
            :columns="generatedCasesColumns"
            :data="generatedCases"
            :row-key="(row) => `${row.case_name}_${row.method}`"
            :checked-row-keys="selectedCaseKeys"
            @update:checked-row-keys="handleCheckedRowKeysChange"
            size="small"
            max-height="400px"
            virtual-scroll
          />
        </NCard>
      </div>

      <template #footer>
        <div class="flex justify-end gap-2">
          <NButton @click="aiGenerateModalVisible = false">取消</NButton>
        </div>
      </template>
    </NModal>
  </CommonPage>
</template>


<script setup>
import {h, ref, reactive, watch, computed, onMounted} from 'vue'
import {
  NButton,
  NDataTable,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NModal,
  NTabs,
  NTabPane,
  NCheckbox,
  NInputGroup,
  NTag,
  NTooltip,
  NUpload,
  NUploadDragger,
  NIcon,
  NText,
  NP,
  NAlert,
  NSpace,
  NInputNumber,
  NCard,
  NList,
  NListItem,
  NThing,
  NSpin,
  useMessage,
  useDialog
} from 'naive-ui'
import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudTable from '@/components/table/CrudTable.vue'
import TheIcon from '@/components/icon/TheIcon.vue'
import { renderIcon } from '@/utils'
import api from '@/api'

const message = useMessage()
const dialog = useDialog()
const $table = ref(null)

// 计算属性：获取当前选中的行数
const selectedCount = computed(() => {
  return $table.value?.selectedRowKeys?.length || 0
})

// 查询条件
const queryItems = ref({
  api_name: null,
  url_path: null,
  method: null,
  project_id: null
})


// 请求方式选项
const methodOptions = [
  {label: 'GET', value: 'GET'},
  {label: 'POST', value: 'POST'},
  {label: 'PUT', value: 'PUT'},
  {label: 'DELETE', value: 'DELETE'},
  {label: 'PATCH', value: 'PATCH'},
  {label: 'OPTIONS', value: 'OPTIONS'},
  {label: 'HEAD', value: 'HEAD'}
]

// 状态选项
const statusOptions = [
  {label: '启用', value: 'active'},
  {label: '禁用', value: 'inactive'},
  {label: '开发中', value: 'developing'},
  {label: '测试中', value: 'testing'},
  {label: '已废弃', value: 'deprecated'}
]

// 项目选项
const projectOptions = ref([])

const editModalVisible = ref(false)

// 初始化编辑表单响应式数据
const editeFormData = ref({
  id: '',
  api_name: '',
  method: '',
  url_path: '',
  params_list: '',
  status: '',
  project_id: ''
});

// 显示编辑弹窗
function showEditModal(row) {
  editeFormData.value = {
    id: row.id,
    api_name: row.api_name,
    method: row.method,
    url_path: row.url_path,
    params_list: row.params_list,
    status: row.status,
    project_id: row.project_id
  }
  editModalVisible.value = true
}

// 获取项目列表
const fetchProjects = async () => {
  try {
    const response = await api.getProjectList({
      page: 1,
      page_size: 100  // 设置较大的页面大小以获取更多项目
    })

    if (response && response.data) {
      projectOptions.value = response.data.map(item => ({
        label: item.name,
        value: item.id
      }))
    } else {
      console.error('项目数据格式不正确:', response)
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
  }
}

// 表格列配置
const columns = [
  {
    type: 'selection',
    disabled(row) {
      return false
    }
  },
  {title: '接口名称', key: 'api_name'},
  {
    title: '请求方式', key: 'method',
    render(row) {
      const methodColorMap = {
        GET: 'success',
        POST: 'info',
        PUT: 'warning',
        DELETE: 'error'
      }
      return h(NTag, {type: methodColorMap[row.method] || 'default'}, {default: () => row.method})
    }
  },
  {
    title: 'URL路径', key: 'url_path',
    render(row) {
      return h(NTooltip, {}, {
        trigger: () => h('span', {style: 'cursor: pointer; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;'}, row.url_path),
        default: () => row.url_path
      })
    }
  },
  {
    title: '请求参数',
    key: 'params_list',
    // 修改请求参数列的render函数
    render(row) {
      return h(NTooltip, {
        triggerProps: {
          style: 'max-width: 300px; text-align: center;'
        },
        style: {
          maxWidth: '500px',
          maxHeight: '300px',
          overflow: 'auto',
          textAlign: 'left'
        }
      }, {
        trigger: () => h('span', {
          style: 'cursor: pointer; max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block; text-align: center;'
        }, row.params_list || '-'),
        default: () => h('pre', {
          style: 'white-space: pre-wrap; word-break: break-all; padding: 8px; margin: 0; max-width: 500px; max-height: none; overflow: visible;'
        }, row.params_list)
      })
    }
  },
  {
    title: '状态',
    key: 'status',
    render(row) {
      const statusMap = {
        active: {type: 'success', text: '启用'},
        inactive: {type: 'default', text: '禁用'},
        developing: {type: 'info', text: '开发中'},
        testing: {type: 'warning', text: '测试中'},
        deprecated: {type: 'error', text: '已废弃'}
      }
      const status = statusMap[row.status] || {type: 'default', text: row.status || '-'}
      return h(NTag, {type: status.type}, {default: () => status.text})
    }
  },
  {
    title: '所属项目',
    key: 'project_id',
    render(row) {
      const project = projectOptions.value.find(p => p.value == row.project_id)
      return project ? project.label : '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    render(row) {
      return h('div', {class: 'flex gap-2'}, [
        h(NButton, {
          size: 'small',
          onClick: () => showEditModal(row)
        }, () => '编辑'),
        h(NButton, {
          size: 'small',
          type: 'primary',
          onClick: () => handleAIGenerate(row)
        }, () => 'AI生成'),
        h(NButton, {
          size: 'small',
          type: 'error',
          onClick: () => handleDelete(row.id)
        }, () => '删除')
      ])
    }
  }
]

// 生成结果表格列配置
const generatedCasesColumns = [
  {
    type: 'selection',
    disabled(row) {
      return false
    }
  },
  {
    title: '用例名称',
    key: 'case_name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '请求方法',
    key: 'method',
    width: 100,
    render(row) {
      const methodColorMap = {
        GET: 'success',
        POST: 'info',
        PUT: 'warning',
        DELETE: 'error'
      }
      return h(NTag, {type: methodColorMap[row.method] || 'default'}, {default: () => row.method})
    }
  },
  {
    title: '请求URL',
    key: 'url',
    width: 250,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '请求参数',
    key: 'params',
    width: 200,
    render(row) {
      if (!row.params) return '-'
      return h(NTooltip, {}, {
        trigger: () => h('span', {
          style: 'cursor: pointer; max-width: 180px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;'
        }, row.params),
        default: () => h('pre', {
          style: 'white-space: pre-wrap; word-break: break-all; padding: 8px; margin: 0; max-width: 400px;'
        }, row.params)
      })
    }
  },
  {
    title: '请求体',
    key: 'body',
    width: 200,
    render(row) {
      if (!row.body) return '-'
      return h(NTooltip, {}, {
        trigger: () => h('span', {
          style: 'cursor: pointer; max-width: 180px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;'
        }, row.body),
        default: () => h('pre', {
          style: 'white-space: pre-wrap; word-break: break-all; padding: 8px; margin: 0; max-width: 400px;'
        }, row.body)
      })
    }
  },
  {
    title: '断言配置',
    key: 'expected_result',
    width: 200,
    render(row) {
      if (!row.expected_result) return '-'
      return h(NTooltip, {}, {
        trigger: () => h('span', {
          style: 'cursor: pointer; max-width: 180px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; display: inline-block;'
        }, row.expected_result),
        default: () => h('pre', {
          style: 'white-space: pre-wrap; word-break: break-all; padding: 8px; margin: 0; max-width: 400px;'
        }, row.expected_result)
      })
    }
  },
  {
    title: '冒烟用例',
    key: 'is_smoke',
    width: 100,
    render(row) {
      return h(NTag, {
        type: row.is_smoke ? 'warning' : 'default'
      }, {
        default: () => row.is_smoke ? '是' : '否'
      })
    }
  }
]



// CrudTable需要的数据获取函数
const getApiImportList = async (params) => {
  try {
    const response = await api.getApiImportList(params)
    return {
      data: response.data || [],
      total: response.total || 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    message.error('加载数据失败: ' + error.message)
    return {
      data: [],
      total: 0
    }
  }
}

// 重置查询条件
const handleReset = () => {
  queryItems.value = {
    api_name: null,
    url_path: null,
    method: null,
    project_id: null
  }
  $table.value?.handleSearch()
}





// 表单处理逻辑
const formRef = ref(null)
const editFormRef = ref(null)
const showModal = ref(false)
const isSubmitting = ref(false)

const formData = reactive({
  project_id: null,
  file: null,
  fileContent: null
})

// 文件列表
const fileList = ref([])

// 解析结果
const parseResult = ref(null)

// 表单验证规则
const formRules = {
  project_id: {
    required: true,
    message: '请选择所属项目',
    trigger: ['blur', 'change']
  }
}


const handleFileChange = ({file}) => {
  if (!file?.file) return

  formData.file = file.file
  fileList.value = [file]
  parseResult.value = null

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const content = JSON.parse(e.target.result)
      formData.fileContent = content

      // 验证是否为有效的Swagger/OpenAPI文档
      if (!content.openapi && !content.swagger) {
        throw new Error('不是有效的Swagger/OpenAPI文档')
      }

      // 统计接口数量
      const pathCount = content.paths ? Object.keys(content.paths).length : 0
      let apiCount = 0
      if (content.paths) {
        Object.values(content.paths).forEach(methods => {
          apiCount += Object.keys(methods).length
        })
      }

      // 计算文件大小
      const fileSize = (file.file.size / 1024).toFixed(2) + ' KB'

      parseResult.value = {
        success: true,
        title: '文件解析成功',
        message: `成功解析Swagger文档，包含 ${pathCount} 个路径，${apiCount} 个接口`,
        count: apiCount,
        fileSize: fileSize
      }

    } catch (error) {
      formData.fileContent = null
      parseResult.value = {
        success: false,
        title: '文件解析失败',
        message: error.message || '无效的JSON格式或Swagger文档格式'
      }
    }
  }
  reader.readAsText(file.file)
}

const handleFileRemove = () => {
  formData.file = null
  formData.fileContent = null
  fileList.value = []
  parseResult.value = null
}

const handleSubmit = async () => {
  try {
    console.log('开始提交，当前表单数据:', {
      project_id: formData.project_id,
      hasFileContent: !!formData.fileContent,
      parseResult: parseResult.value
    })

    // 手动验证必填字段
    if (!formData.project_id) {
      message.error('请选择所属项目')
      return
    }

    if (!formData.fileContent) {
      message.error('请先选择并解析Swagger文件')
      return
    }

    if (!parseResult.value || !parseResult.value.success) {
      message.error('文件解析失败，请重新选择文件')
      return
    }

    console.log('验证通过，开始提交到后端')
    isSubmitting.value = true

    const response = await api.uploadApiImport({
      fileContent: JSON.stringify(formData.fileContent, null, 2),
      projectId: formData.project_id
    })

    console.log('后端响应:', response)
    message.success(`导入成功！共导入 ${response.data?.count || 0} 个接口`)
    showModal.value = false

    // 重置表单
    formData.project_id = null
    formData.file = null
    formData.fileContent = null
    fileList.value = []
    parseResult.value = null

    // 刷新列表
    $table.value?.handleSearch()

  } catch (error) {
    console.error('导入失败:', error)
    const errorMessage = error.response?.data?.msg || error.message || '导入失败，请重试'
    message.error(`导入失败: ${errorMessage}`)
  } finally {
    isSubmitting.value = false
  }
}

// 打开导入弹窗
const openImportModal = () => {
  showModal.value = true
  // 重置表单数据
  formData.project_id = null
  formData.file = null
  formData.fileContent = null
  fileList.value = []
  parseResult.value = null
}

const editeSubmit = async () => {
  try {
    isSubmitting.value = true
    const response = await api.updateApiImport({
      id: editeFormData.value.id,
      api_name: editeFormData.value.api_name,
      method: editeFormData.value.method,
      url_path: editeFormData.value.url_path,
      params_list: editeFormData.value.params_list,
      status: editeFormData.value.status,
      project_id: editeFormData.value.project_id
    })
    console.log('编辑结果:', response)
    message.success('编辑成功')
    editModalVisible.value = false
    $table.value?.handleSearch()
  } catch (error) {
    message.error(`编辑失败: ${error.message}`)
    $table.value?.handleSearch()
  } finally {
    isSubmitting.value = false
  }
}

// 添加参数
const addParam = () => {
  formData.params.push({
    checked: true,
    key: '',
    value: '',
    description: ''
  })
}

// 删除参数
const removeParam = (index) => {
  formData.params.splice(index, 1)
}

// 添加头部
const addHeader = () => {
  formData.headers.push({
    checked: true,
    key: '',
    value: '',
    description: ''
  })
}

// 删除头部
const removeHeader = (index) => {
  formData.headers.splice(index, 1)
}

// 解析参数列表字符串为对象数组
const parseParamsList = (paramsListStr) => {
  if (!paramsListStr) return []
  try {
    const paramsObj = JSON.parse(paramsListStr)
    const result = []
    for (const key in paramsObj) {
      result.push({
        checked: true,
        key,
        value: paramsObj[key],
        description: ''
      })
    }
    return result
  } catch (e) {
    console.error('解析参数列表失败:', e)
    return []
  }
}

// 解析请求头字符串为对象数组
const parseHeaders = (headersStr) => {
  if (!headersStr) return []
  try {
    const headersObj = JSON.parse(headersStr)
    const result = []
    for (const key in headersObj) {
      result.push({
        checked: true,
        key,
        value: headersObj[key],
        description: ''
      })
    }
    return result
  } catch (e) {
    console.error('解析请求头失败:', e)
    return []
  }
}

function importApiModal(row) {
  // 重置表单数据

  formData.project_id = null
  formData.file = null
  formData.fileContent = null  // 添加这一行

  if (row) {

    formData.project_id = row.project_id

  }

  showModal.value = true
}


// 删除处理
async function handleDelete(id) {
  try {
    await api.deleteApiImport(id)
    message.success('删除成功')
    $table.value?.handleSearch()
  } catch (error) {
    message.error('删除失败: ' + error.message)
  }
}

// 批量删除处理
async function handleBatchDelete() {
  const selectedKeys = $table.value?.selectedRowKeys || []
  if (selectedKeys.length === 0) {
    message.warning('请先选择要删除的接口')
    return
  }

  dialog.warning({
    title: '确认删除',
    content: `确定要删除选中的接口吗？此操作不可恢复。`,
    positiveText: '确定删除',
    negativeText: '取消',
    onPositiveClick: async () => {
      try {
        await api.batchDeleteApiImport({ ids: selectedKeys })
        message.success(`成功删除接口`)

        // 刷新数据并重置到第一页
        $table.value?.handleSearch()

        // 页面刷新操作 - 重新加载页面数据
        // setTimeout(() => {
        //   window.location.reload()
        // }, ) // 1秒后刷新页面，给用户看到成功消息的时间

      } catch (error) {
        message.error('批量删除失败: ' + error.message)
      }
    }
  })
}

// AI生成相关状态
const aiGenerateModalVisible = ref(false)
const aiGenerating = ref(false)
const aiGenerateForm = ref({
  generate_count: 3,
  prompt_template_id: null,
  ai_model_config_id: null,
  project_id: null,
  module_id: null
})
const generatedCases = ref([])
const selectedCases = ref([])
const selectedCaseKeys = ref([])
const currentApiImport = ref(null)
const moduleOptions = ref([])
const promptTemplateOptions = ref([])
const aiModelOptions = ref([])

// 获取提示词模板列表
const fetchPromptTemplates = async () => {
  try {
    const response = await api.getPromptTemplatesByCategory('api_test_case')
    if (response && response.data) {
      promptTemplateOptions.value = response.data.map(item => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取提示词模板失败:', error)
  }
}

// 获取AI模型配置列表
const fetchAiModels = async () => {
  try {
    const response = await api.getAiModelConfigList({ page: 1, page_size: 100 })
    if (response && response.data && response.data.data) {
      aiModelOptions.value = response.data.data.map(item => ({
        label: item.name,
        value: item.id
      }))
    }
  } catch (error) {
    console.error('获取AI模型配置失败:', error)
  }
}

// AI生成测试用例
const handleAIGenerate = async (row) => {
  currentApiImport.value = row
  aiGenerateForm.value = {
    generate_count: 3,
    prompt_template_id: null,
    ai_model_config_id: null,
    project_id: row.project_id, // 默认使用当前接口的项目
    module_id: null
  }
  generatedCases.value = []
  selectedCases.value = []
  selectedCaseKeys.value = []

  // 加载选项数据
  await Promise.all([
    fetchPromptTemplates(),
    fetchAiModels(),
    row.project_id ? fetchModulesByProject(row.project_id) : Promise.resolve()
  ])

  aiGenerateModalVisible.value = true
}

// 获取项目下的模块列表
const fetchModulesByProject = async (projectId) => {
  try {
    const response = await api.getModuleList({
      project_id: projectId
    })

    if (response && response.data) {
      // 将树形结构扁平化为选项列表
      const flattenModules = (modules, prefix = '') => {
        let result = []
        modules.forEach(module => {
          const label = prefix ? `${prefix} / ${module.name}` : module.name
          result.push({
            label: label,
            value: module.id
          })
          if (module.children && module.children.length > 0) {
            result = result.concat(flattenModules(module.children, label))
          }
        })
        return result
      }

      moduleOptions.value = flattenModules(response.data)
    }
  } catch (error) {
    console.error('获取模块列表失败:', error)
    moduleOptions.value = []
  }
}

// 处理项目变化
const handleProjectChange = (projectId) => {
  aiGenerateForm.value.module_id = null
  moduleOptions.value = []
  if (projectId) {
    fetchModulesByProject(projectId)
  }
}

// 执行AI生成
const executeAIGenerate = async () => {
  try {
    aiGenerating.value = true

    const response = await api.aiGenerateApiTestCases({
      api_import_id: currentApiImport.value.id,
      generate_count: aiGenerateForm.value.generate_count,
      prompt_template_id: aiGenerateForm.value.prompt_template_id,
      ai_model_config_id: aiGenerateForm.value.ai_model_config_id
    })

    if (response.data.success) {
      generatedCases.value = response.data.generated_cases || []
      // 默认全选
      selectedCases.value = [...generatedCases.value]
      selectedCaseKeys.value = generatedCases.value.map(case_item =>
        `${case_item.case_name}_${case_item.method}`
      )
      message.success(`成功生成 ${generatedCases.value.length} 个测试用例`)
    } else {
      message.error(response.data.error_message || 'AI生成失败')
    }
  } catch (error) {
    console.error('AI生成失败:', error)
    message.error('AI生成失败: ' + (error.message || '未知错误'))
  } finally {
    aiGenerating.value = false
  }
}

// 保存选中的测试用例
const saveSelectedCases = async () => {
  if (selectedCases.value.length === 0) {
    message.warning('请至少选择一个测试用例')
    return
  }

  if (!aiGenerateForm.value.project_id) {
    message.warning('请选择所属项目')
    return
  }

  try {
    // 为每个测试用例添加项目和模块信息
    const casesWithProjectInfo = selectedCases.value.map(case_item => ({
      ...case_item,
      project_id: aiGenerateForm.value.project_id,
      module_id: aiGenerateForm.value.module_id
    }))

    const response = await api.saveGeneratedApiTestCases({
      api_import_id: currentApiImport.value.id,
      selected_cases: casesWithProjectInfo
    })

    message.success(`成功保存 ${response.data.saved_count} 个测试用例`)
    aiGenerateModalVisible.value = false
  } catch (error) {
    console.error('保存测试用例失败:', error)
    message.error('保存失败: ' + (error.message || '未知错误'))
  }
}

// 处理表格选择变化
const handleCheckedRowKeysChange = (keys) => {
  selectedCaseKeys.value = keys
  selectedCases.value = generatedCases.value.filter(case_item =>
    keys.includes(`${case_item.case_name}_${case_item.method}`)
  )
}

// 全选/取消全选
const toggleSelectAll = () => {
  if (selectedCases.value.length === generatedCases.value.length) {
    selectedCases.value = []
    selectedCaseKeys.value = []
  } else {
    selectedCases.value = [...generatedCases.value]
    selectedCaseKeys.value = generatedCases.value.map(case_item =>
      `${case_item.case_name}_${case_item.method}`
    )
  }
}

// 初始化
onMounted(async () => {
  await fetchProjects()
  // 自动加载数据
  $table.value?.handleSearch()
})
</script>

<style scoped>
.mb-4 {
  margin-bottom: 16px;
}

.mr-1 {
  margin-right: 4px;
}

.mr-2 {
  margin-right: 8px;
}

.ml-2 {
  margin-left: 8px;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.items-center {
  align-items: center;
}

.justify-end {
  justify-content: flex-end;
}

.gap-2 {
  gap: 8px;
}

.custom-table {
  border: 1px solid #eee;
  border-radius: 3px;
}

.table-header {
  background-color: #f9f9f9;
  padding: 8px;
  font-weight: bold;
  border-bottom: 1px solid #eee;
}

.table-row {
  padding: 8px;
  border-bottom: 1px solid #eee;
}

.table-row:last-child {
  border-bottom: none;
}

/* 添加表格样式 */
.n-data-table {
  border: 1px solid #eee;
  border-radius: 4px;
}

.n-data-table-th {
  background-color: #f9f9f9;
  font-weight: bold;
}

.n-data-table-tr:nth-child(even) {
  background-color: #fafafa;
}

/* AI生成弹窗样式 */
.grid {
  display: grid;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.gap-4 {
  gap: 1rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.max-h-400px {
  max-height: 400px;
}

.overflow-y-auto {
  overflow-y: auto;
}

.bg-blue-50 {
  background-color: #eff6ff;
}

.border-blue-200 {
  border-color: #bfdbfe;
}

.border {
  border-width: 1px;
  border-style: solid;
  border-color: #e5e7eb;
}

.rounded {
  border-radius: 0.375rem;
}

.p-3 {
  padding: 0.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-gray-600 {
  color: #4b5563;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}
</style>

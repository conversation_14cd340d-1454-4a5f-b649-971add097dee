<template>
  <NLayout has-sider wh-full>
    <NLayoutSider
      bordered
      content-style="padding: 24px;"
      :collapsed-width="0"
      :width="240"
      show-trigger="arrow-circle"
    >
      <h1>项目列表</h1>
      <br />
      <NTree
        block-line
        :data="projectOption"
        key-field="id"
        label-field="name"
        default-expand-all
        :node-props="nodeProps"
        :selected-keys="selectedKeys"
      >
      </NTree>
    </NLayoutSider>
    <NLayoutContent>
      <CommonPage show-footer :title="selectedProjectName ? `${selectedProjectName} - 功能测试用例` : '功能测试用例'">
        <template #action>
          <div style="display: flex; gap: 12px; align-items: center;">
            <!-- 导入测试用例按钮 -->
            <NButton
              v-if="selectedProjectId"
              type="info"
              size="small"
              @click="handleImport"
            >
              <TheIcon icon="material-symbols:upload" :size="18" class="mr-5" />导入测试用例
            </NButton>

            <!-- 重置编号按钮 -->
            <NPopconfirm
              v-if="selectedProjectId"
              @positive-click="handleResetNumbers"
            >
              <template #trigger>
                <NButton type="warning" size="small">
                  <TheIcon icon="material-symbols:refresh" :size="18" class="mr-5" />重置编号
                </NButton>
              </template>
              确认重置该项目下所有测试用例的编号吗？
            </NPopconfirm>

            <!-- 新建测试用例按钮 -->
            <NButton
              v-if="selectedProjectId"
              type="primary"
              size="small"
              @click="handleAdd"
            >
              <TheIcon icon="material-symbols:add" :size="18" class="mr-5" />新建测试用例
            </NButton>
          </div>
        </template>
        <!-- 表格 -->
        <CrudTable
          ref="$table"
          v-model:query-items="queryItems"
          :columns="columns"
          :get-data="testCaseApi.getTestCases"
          @reset="handleReset"
        >
          <template #queryBar>
            <QueryBarItem label="" :label-width="80">
              <NInput
                v-model:value="queryItems.case_name"
                clearable
                type="text"
                placeholder="请输入用例名称"
                @keypress.enter="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="80">
              <NSelect
                v-model:value="queryItems.case_level"
                clearable
                :options="levelOptions"
                placeholder="等级"
                style="width: 100px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="80">
              <NSelect
                v-model:value="queryItems.module_id"
                clearable
                :options="moduleOptions"
                placeholder="模块"
                style="width: 100px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="50">
              <NSelect
                v-model:value="queryItems.status"
                clearable
                :options="statusOptions"
                placeholder="状态"
                style="width: 100px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="50">
              <NSelect
                v-model:value="queryItems.source"
                clearable
                :options="sourceOptions"
                placeholder="来源"
                style="width: 100px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
            <QueryBarItem label="" :label-width="80">
              <NSelect
                v-model:value="queryItems.is_smoke"
                clearable
                :options="[
                  { label: '是', value: true },
                  { label: '否', value: false }
                ]"
                placeholder="冒烟"
                style="width: 100px"
                @update:value="$table?.handleSearch()"
              />
            </QueryBarItem>
          </template>
        </CrudTable>
      </CommonPage>
    </NLayoutContent>
  </NLayout>

  <!-- 新增/编辑弹窗 -->
  <CrudModal
    v-model:visible="modalVisible"
    :title="modalTitle"
    :loading="modalLoading"
    :show-footer="true"
    @save="handleSave"
    @close="handleModalClose"
    width="900px"
  >
    <NForm
      ref="modalFormRef"
      label-placement="left"
      label-align="left"
      :label-width="100"
      :model="modalForm"
      :rules="modalRules"
    >
      <NFormItem label="用例名称" path="case_name">
        <NInput v-model:value="modalForm.case_name" clearable placeholder="请输入用例名称" />
      </NFormItem>

      <!-- 第一行：用例等级、所属项目、所属模块 -->
      <div style="display: flex; gap: 16px;">
        <NFormItem label="用例等级" path="case_level" style="flex: 1;">
          <NSelect
            v-model:value="modalForm.case_level"
            :options="levelOptions"
            placeholder="请选择用例等级"
          />
        </NFormItem>
        <NFormItem label="所属项目" path="project_id" style="flex: 1;">
          <NSelect
            v-model:value="modalForm.project_id"
            :options="projectOption.map(p => ({ label: p.name, value: String(p.id) }))"
            placeholder="请选择所属项目"
            @update:value="handleProjectChange"
          />
        </NFormItem>
        <NFormItem label="所属模块" path="module_id" style="flex: 1;">
          <NSelect
            v-model:value="modalForm.module_id"
            :options="moduleOptions"
            placeholder="请选择所属模块"
            clearable
          />
        </NFormItem>
      </div>

      <NFormItem label="前置条件" path="precondition">
        <NInput
          v-model:value="modalForm.precondition"
          type="textarea"
          :rows="3"
          placeholder="请输入前置条件（可选）"
        />
      </NFormItem>
      <NFormItem label="用例步骤" path="test_steps">
        <NInput
          v-model:value="modalForm.test_steps"
          type="textarea"
          :rows="5"
          placeholder="请输入用例步骤"
        />
      </NFormItem>
      <NFormItem label="预期结果" path="expected_result">
        <NInput
          v-model:value="modalForm.expected_result"
          type="textarea"
          :rows="3"
          placeholder="请输入预期结果"
        />
      </NFormItem>

      <!-- 第二行：是否冒烟用例、状态、来源 -->
      <div style="display: flex; gap: 16px;">
        <NFormItem label="是否冒烟用例" path="is_smoke" style="flex: 1;">
          <NSwitch v-model:value="modalForm.is_smoke" />
        </NFormItem>
        <NFormItem label="状态" path="status" style="flex: 1;">
          <NSelect
            v-model:value="modalForm.status"
            :options="statusOptions"
            placeholder="请选择状态"
          />
        </NFormItem>
        <NFormItem label="来源" path="source" style="flex: 1;">
          <NSelect
            v-model:value="modalForm.source"
            :options="sourceOptions"
            placeholder="请选择来源"
          />
        </NFormItem>
      </div>
    </NForm>
  </CrudModal>

  <!-- 复制用例弹窗 -->
  <CrudModal
    v-model:visible="copyModalVisible"
    title="复制测试用例"
    @save="executeCopy"
  >
    <NForm
      label-placement="left"
      label-align="left"
      :label-width="100"
      :model="copyForm"
    >
      <NFormItem label="新用例名称" path="case_name">
        <NInput v-model:value="copyForm.case_name" clearable placeholder="请输入新用例名称" />
      </NFormItem>
      <NFormItem label="目标项目" path="project_id">
        <NSelect
          v-model:value="copyForm.project_id"
          :options="projectOption.map(p => ({ label: p.name, value: p.id }))"
          placeholder="请选择目标项目"
        />
      </NFormItem>
    </NForm>
  </CrudModal>

  <!-- 导入测试用例弹窗 -->
  <NModal
    v-model:show="importModalVisible"
    preset="card"
    title="导入功能测试用例"
    style="width: 600px;"
    :mask-closable="false"
    :closable="true"
  >
    <div>
      <NAlert type="info" style="margin-bottom: 16px;">
        <template #header>导入说明</template>
        <div>
          <p>1. 请先下载模板文件，按照模板格式填写测试用例数据</p>
          <p>2. 用例等级只能填写：高、中、低</p>
          <p>3. 是否冒烟用例只能填写：是、否</p>
          <p>4. 导入的用例状态默认为"待审核"，来源为"人工"</p>
        </div>
      </NAlert>

      <div style="margin-bottom: 16px;">
        <NButton type="primary" @click="handleDownloadTemplate" :loading="downloadLoading">
          <TheIcon icon="material-symbols:download" :size="18" class="mr-5" />下载模板
        </NButton>
      </div>

      <!-- 模块选择 -->
      <div style="margin-bottom: 16px;">
        <NFormItem label="默认模块" label-placement="left" :label-width="80">
          <NSelect
            v-model:value="selectedModuleId"
            :options="moduleOptions"
            placeholder="选择默认模块（可选）"
            clearable
            style="width: 100%"
          />
        </NFormItem>
        <NText depth="3" style="font-size: 12px; margin-top: 4px;">
          选择后，所有导入的用例都将分配到此模块。如果不选择，将根据Excel中的"所属模块"列进行匹配。
        </NText>
      </div>

      <NUpload
        ref="uploadRef"
        :file-list="fileList"
        :max="1"
        accept=".xlsx,.xls"
        @change="handleFileChange"
        @remove="handleFileRemove"
      >
        <NUploadDragger>
          <div style="margin-bottom: 12px;">
            <TheIcon icon="material-symbols:upload-file" :size="48" style="color: #18a058;" />
          </div>
          <NText style="font-size: 16px">点击或者拖动文件到该区域来上传</NText>
          <NP depth="3" style="margin: 8px 0 0 0">
            支持 .xlsx 和 .xls 格式的Excel文件
          </NP>
        </NUploadDragger>
      </NUpload>

      <!-- 导入结果显示 -->
      <div v-if="importResult" style="margin-top: 16px;">
        <NAlert
          :type="importResult.success ? 'success' : 'error'"
          :title="importResult.message"
        >
          <div v-if="importResult.data">
            <p v-if="importResult.data.success_count > 0">
              成功导入：{{ importResult.data.success_count }} 条
            </p>
            <p v-if="importResult.data.error_count > 0">
              失败：{{ importResult.data.error_count }} 条
            </p>
            <div v-if="importResult.data.error_messages && importResult.data.error_messages.length > 0">
              <p style="margin-top: 8px; font-weight: bold;">错误详情：</p>
              <ul style="margin: 4px 0; padding-left: 20px;">
                <li v-for="(error, index) in importResult.data.error_messages" :key="index" style="margin: 2px 0;">
                  {{ error }}
                </li>
              </ul>
            </div>
          </div>
        </NAlert>
      </div>
    </div>

    <template #footer>
      <div style="text-align: right;">
        <NButton @click="handleCloseImportModal" style="margin-right: 12px;">取消</NButton>
        <NButton
          type="primary"
          @click="handleExecuteImport"
          :loading="importLoading"
          :disabled="fileList.length === 0"
        >
          开始导入
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<script setup>
import { h, onMounted, ref, computed } from 'vue'
import {
  NLayout,
  NLayoutSider,
  NLayoutContent,
  NTree,
  NButton,
  NInput,
  NSelect,
  NSwitch,
  NTag,
  NPopconfirm,
  NForm,
  NFormItem,
  NModal,
  NAlert,
  NUpload,
  NUploadDragger,
  NText,
  NP,
  NPopover,
  NSpace,
  useMessage,
  useDialog
} from 'naive-ui'

import CommonPage from '@/components/page/CommonPage.vue'
import QueryBarItem from '@/components/query-bar/QueryBarItem.vue'
import CrudModal from '@/components/table/CrudModal.vue'
import CrudTable from '@/components/table/CrudTable.vue'

import { formatDate, renderIcon } from '@/utils'
import { useCRUD } from '@/composables'
import testCaseApi from '@/api/testCase.js'
import projectApi from '@/api/project'
import TheIcon from '@/components/icon/TheIcon.vue'

defineOptions({ name: '功能测试用例' })

const $message = useMessage()
const $dialog = useDialog()
const $table = ref(null)

// 项目相关
const projectOption = ref([])
const selectedKeys = ref([])
const selectedProjectId = ref(null)
const selectedProjectName = ref('')
const moduleOptions = ref([])

// 树节点属性
const nodeProps = ({ option }) => {
  return {
    onClick() {
      selectedKeys.value = [option.id]
      selectedProjectId.value = option.id
      selectedProjectName.value = option.name
      queryItems.value.project_id = option.id
      // 加载该项目的模块
      loadModuleOptions(option.id)
      // 刷新表格
      $table.value?.handleSearch()
    }
  }
}

// 选项数据
const levelOptions = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' }
]

const statusOptions = [
  { label: '待审核', value: 'pending' },
  { label: '已审核', value: 'approved' }
]

const sourceOptions = [
  { label: '人工', value: 'manual' },
  { label: 'AI', value: 'ai' }
]

// 复制相关状态
const copyModalVisible = ref(false)
const copyForm = ref({
  id: null,
  case_name: '',
  project_id: null
})

// 导入相关状态
const importModalVisible = ref(false)
const importLoading = ref(false)
const downloadLoading = ref(false)
const fileList = ref([])
const uploadRef = ref(null)
const importResult = ref(null)
const selectedModuleId = ref(null)

// CRUD配置
const {
  modalVisible,
  modalTitle,
  modalLoading,
  handleAdd: originalHandleAdd,
  handleDelete,
  handleEdit: originalHandleEdit,
  handleSave: originalHandleSave,
  modalForm,
  modalFormRef,
  handleModalClose
} = useCRUD({
  name: '功能测试用例',
  initForm: {
    case_name: '',
    case_level: 'medium',
    precondition: '',
    test_steps: '',
    expected_result: '',
    is_smoke: false,
    status: 'pending',
    source: 'manual',
    project_id: '',
    module_id: null,
  },
  doCreate: testCaseApi.createTestCase,
  doUpdate: testCaseApi.updateTestCase,
  doDelete: testCaseApi.deleteTestCase,
  refresh: () => $table.value?.handleSearch()
})

// 表单验证规则
const modalRules = {
  case_name: [{ required: true, message: '请输入用例名称', trigger: 'blur' }],
  case_level: [{ required: true, message: '请选择用例等级', trigger: 'change' }],
  project_id: [{ required: true, message: '请选择所属项目', trigger: 'change' }],
  test_steps: [{ required: true, message: '请输入用例步骤', trigger: 'blur' }],
  expected_result: [{ required: true, message: '请输入预期结果', trigger: 'blur' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  source: [{ required: true, message: '请选择来源', trigger: 'change' }]
}

// 查询参数
const queryItems = ref({
  case_name: null,
  case_level: null,
  module_id: null,
  status: null,
  source: null,
  is_smoke: null,
  project_id: null
})

// 表格列配置
const columns = [
  {
    title: '用例编号',
    key: 'case_number',
    width: 120,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '用例名称',
    key: 'case_name',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '用例等级',
    key: 'case_level',
    width: 100,
    render(row) {
      const levelMap = {
        low: { text: '低', type: 'info' },
        medium: { text: '中', type: 'warning' },
        high: { text: '高', type: 'error' }
      }
      const level = levelMap[row.case_level] || { text: row.case_level, type: 'default' }
      return h(NTag, { type: level.type, size: 'small' }, { default: () => level.text })
    }
  },
  {
    title: '前置条件',
    key: 'precondition',
    width: 150,
    ellipsis: {
      tooltip: true
    },
    render(row) {
      return row.precondition || '-'
    }
  },
  {
    title: '用例步骤',
    key: 'test_steps',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '预期结果',
    key: 'expected_result',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '冒烟用例',
    key: 'is_smoke',
    width: 100,
    render(row) {
      return h(NTag, { 
        type: row.is_smoke ? 'success' : 'default', 
        size: 'small' 
      }, { 
        default: () => row.is_smoke ? '是' : '否' 
      })
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row) {
      const statusMap = {
        pending: { text: '待审核', type: 'warning' },
        approved: { text: '已审核', type: 'success' }
      }
      const status = statusMap[row.status] || { text: row.status, type: 'default' }

      return h(NPopover, {
        trigger: 'click',
        placement: 'bottom'
      }, {
        trigger: () => h(NTag, {
          type: status.type,
          size: 'small',
          style: 'cursor: pointer;'
        }, { default: () => status.text }),
        default: () => h('div', {
          style: 'padding: 8px;'
        }, [
          h('div', {
            style: 'margin-bottom: 8px; font-weight: 500; color: #666;'
          }, '选择状态:'),
          h(NSpace, {
            vertical: true,
            size: 'small'
          }, {
            default: () => Object.entries(statusMap).map(([value, config]) =>
              h(NTag, {
                type: config.type,
                size: 'small',
                style: 'cursor: pointer; width: 80px; text-align: center;',
                bordered: row.status === value,
                onClick: () => handleUpdateStatus(row.id, value)
              }, {
                default: () => config.text
              })
            )
          })
        ])
      })
    }
  },
  {
    title: '来源',
    key: 'source',
    width: 80,
    render(row) {
      const sourceMap = {
        manual: { text: '人工', type: 'info' },
        ai: { text: 'AI', type: 'success' }
      }
      const source = sourceMap[row.source] || { text: row.source, type: 'default' }
      return h(NTag, { type: source.type, size: 'small' }, { default: () => source.text })
    }
  },
  {
    title: '创建时间',
    key: 'created_at',
    width: 180,
    render(row) {
      return formatDate(row.created_at)
    }
  },
  {
    title: '创建用户',
    key: 'username',
    width: 120,
    render(row) {
      return row.username || '-'
    }
  },
  {
    title: '操作',
    key: 'actions',
    width: 240,
    align: 'center',
    fixed: 'right',
    hideInExcel: true,
    render(row) {
      return h('div', { style: 'display: flex; gap: 8px; justify-content: center;' }, [
        h(
          NButton,
          {
            size: 'small',
            type: 'primary',
            secondary: true,
            onClick: () => handleEdit(row)
          },
          { default: () => '编辑' }
        ),
        h(
          NButton,
          {
            size: 'small',
            type: 'info',
            secondary: true,
            onClick: () => handleCopy(row)
          },
          { default: () => '复制' }
        ),
        h(
          NPopconfirm,
          {
            onPositiveClick: () => handleDelete({ id: row.id })
          },
          {
            default: () => '确认删除',
            trigger: () => h(
              NButton,
              {
                size: 'small',
                type: 'error'
              },
              { default: () => '删除' }
            )
          }
        )
      ])
    }
  }
]

// API实例
const api = testCaseApi

// 项目选择处理
let lastClickedProjectId = null

const handleTreeSelect = (selectedKeys, option) => {
  if (selectedKeys.length > 0) {
    const projectId = selectedKeys[0]
    const project = projectOption.value.find(p => p.id === projectId)
    if (project && lastClickedProjectId !== projectId) {
      handleProjectSelect(project)
    }
  }
}

// 处理项目选择
const handleProjectSelect = async (project) => {
  try {
    selectedProjectId.value = project.id
    selectedProjectName.value = project.name
    selectedKeys.value = [project.id]
    lastClickedProjectId = project.id

    // 加载模块选项
    await loadModuleOptions(project.id)

    // 更新查询条件并刷新表格
    queryItems.value.project_id = project.id
    queryItems.value.module_id = null
    $table.value?.handleSearch()
  } catch (error) {
    console.error('选择项目失败:', error)
  }
}

// 加载项目列表
const loadProjectList = async () => {
  try {
    const response = await projectApi.getProjectList()
    projectOption.value = response.data || []

    // 默认选择第一个项目
    if (projectOption.value.length > 0 && !selectedProjectId.value) {
      const firstProject = projectOption.value[0]
      selectedProjectId.value = firstProject.id
      selectedProjectName.value = firstProject.name
      selectedKeys.value = [firstProject.id]
      queryItems.value.project_id = firstProject.id
      await loadModuleOptions(firstProject.id)
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('加载项目列表失败:', error)
  }
}

// 加载模块选项
const loadModuleOptions = async (projectId) => {
  try {
    const response = await projectApi.getProjectModuleTree({ project_id: projectId })
    const modules = response.data || []

    // 扁平化模块树，保持层级关系
    const flattenModules = (modules, result = [], level = 0) => {
      modules.forEach(module => {
        const prefix = '　'.repeat(level) // 使用全角空格缩进
        result.push({
          label: `${prefix}${module.name}`,
          value: module.id
        })
        if (module.children && module.children.length > 0) {
          flattenModules(module.children, result, level + 1)
        }
      })
      return result
    }

    moduleOptions.value = flattenModules(modules)
  } catch (error) {
    console.error('加载模块选项失败:', error)
    moduleOptions.value = []
  }
}

// 复制用例
const handleCopy = (row) => {
  copyForm.value = {
    id: row.id,
    case_name: `${row.case_name}_副本`,
    project_id: selectedProjectId.value || row.project_id
  }
  copyModalVisible.value = true
}

// 执行复制
const executeCopy = async () => {
  try {
    await testCaseApi.copyTestCase(copyForm.value)
    $message.success('用例复制成功')
    copyModalVisible.value = false
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('复制失败: ' + error.message)
  }
}

// 重置编号
const handleResetNumbers = async () => {
  try {
    await testCaseApi.resetTestCaseNumbers(selectedProjectId.value)
    $message.success('编号重置成功')
    $table.value?.handleSearch()
  } catch (error) {
    $message.error('重置失败: ' + error.message)
  }
}

// 新增函数
const handleAdd = async () => {
  originalHandleAdd()

  if (selectedProjectId.value) {
    modalForm.value.project_id = String(selectedProjectId.value)
    await loadModuleOptions(selectedProjectId.value)
  }
}

// 编辑函数
const handleEdit = async (row) => {
  const editRow = { ...row }
  if (editRow.project_id !== undefined && editRow.project_id !== null) {
    editRow.project_id = String(editRow.project_id)
    await loadModuleOptions(editRow.project_id)
  }
  originalHandleEdit(editRow)
}

// 保存函数
const handleSave = async () => {
  try {
    await originalHandleSave()

    // 保存成功后刷新表格
    const projectId = selectedProjectId.value || modalForm.value.project_id
    if (projectId) {
      queryItems.value.project_id = projectId
      $table.value?.handleSearch()
    }
  } catch (error) {
    console.error('保存失败:', error)
    $table.value?.handleSearch()
  }
}

// 处理项目变化
const handleProjectChange = async (projectId) => {
  if (projectId) {
    await loadModuleOptions(projectId)
    modalForm.value.module_id = null
  }
}

// 重置处理
const handleReset = () => {
  queryItems.value = {
    case_name: '',
    case_level: '',
    module_id: null,
    status: '',
    source: '',
    is_smoke: null,
    project_id: selectedProjectId.value
  }
}

// 更新状态
const handleUpdateStatus = async (testCaseId, newStatus) => {
  try {
    // 获取当前测试用例的完整信息
    const currentData = $table.value?.tableData?.find(item => item.id === testCaseId)
    if (!currentData) {
      $message.error('未找到测试用例数据')
      return
    }

    // 构建更新数据
    const updateData = {
      id: testCaseId,
      case_name: currentData.case_name,
      case_level: currentData.case_level,
      precondition: currentData.precondition,
      test_steps: currentData.test_steps,
      expected_result: currentData.expected_result,
      is_smoke: currentData.is_smoke,
      status: newStatus,
      source: currentData.source,
      project_id: currentData.project_id,
      module_id: currentData.module_id
    }

    await testCaseApi.updateTestCase(updateData)
    $message.success('状态更新成功')

    // 刷新表格
    $table.value?.handleSearch()
  } catch (error) {
    console.error('更新状态失败:', error)
    $message.error('状态更新失败: ' + (error.message || '未知错误'))
  }
}

// 导入相关函数
const handleImport = () => {
  importModalVisible.value = true
  importResult.value = null
  fileList.value = []
  selectedModuleId.value = null
  // 加载模块选项
  if (selectedProjectId.value) {
    loadModuleOptions(selectedProjectId.value)
  }
}

const handleDownloadTemplate = async () => {
  try {
    downloadLoading.value = true
    const response = await testCaseApi.downloadTemplate()

    // 检查响应是否为Blob
    let blob
    if (response instanceof Blob) {
      blob = response
    } else if (response.data instanceof Blob) {
      blob = response.data
    } else {
      // 如果响应不是Blob，创建一个Blob
      blob = new Blob([response.data || response], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
      })
    }

    // 创建下载链接
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '功能测试用例导入模板.xlsx'
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    $message.success('模板下载成功')
  } catch (error) {
    console.error('下载模板失败:', error)
    $message.error('下载模板失败: ' + (error.response?.data?.msg || error.message))
  } finally {
    downloadLoading.value = false
  }
}

const handleFileChange = (options) => {
  fileList.value = options.fileList
  importResult.value = null
}

const handleFileRemove = () => {
  fileList.value = []
  importResult.value = null
}

const handleExecuteImport = async () => {
  if (fileList.value.length === 0) {
    $message.error('请选择要导入的文件')
    return
  }

  if (!selectedProjectId.value) {
    $message.error('请先选择项目')
    return
  }

  try {
    importLoading.value = true
    importResult.value = null

    const params = {
      file: fileList.value[0].file,
      project_id: selectedProjectId.value
    }

    if (selectedModuleId.value) {
      params.module_id = selectedModuleId.value
    }

    const response = await testCaseApi.importTestCases(params)

    if (response.code === 200) {
      importResult.value = {
        success: true,
        message: response.msg,
        data: response.data
      }

      // 如果有成功导入的数据，刷新表格
      if (response.data && response.data.success_count > 0) {
        $table.value?.handleSearch()
      }
    } else {
      importResult.value = {
        success: false,
        message: response.msg || '导入失败',
        data: response.data
      }
    }
  } catch (error) {
    console.error('导入失败:', error)
    importResult.value = {
      success: false,
      message: '导入失败: ' + error.message,
      data: null
    }
  } finally {
    importLoading.value = false
  }
}

const handleCloseImportModal = () => {
  importModalVisible.value = false
  importResult.value = null
  fileList.value = []
  selectedModuleId.value = null
}

// 生命周期
onMounted(() => {
  loadProjectList()
})
</script>

<style scoped>
.n-layout-page-header {
  display: flex;
  flex-direction: column;
  min-height: 0;
  flex: 1;
}
</style>
